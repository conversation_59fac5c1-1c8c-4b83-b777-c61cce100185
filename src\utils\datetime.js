import Vue from 'vue';
import i18n from '@/i18n';
Vue.prototype.$handleTimestamp = handleTimestamp;
// 秒转日期和时间
export function handleTimestamp(ts) {
  let temp = new Date(ts * 1000),
    y = temp.getFullYear(),
    m = temp.getMonth() + 1,
    d = temp.getDate(),
    h = temp.getHours(),
    min = temp.getMinutes(),
    sec = temp.getSeconds();
  m = m < 10 ? '0' + m : m;
  d = d < 10 ? '0' + d : d;
  h = h < 10 ? '0' + h : h;
  min = min < 10 ? '0' + min : min;
  sec = sec < 10 ? '0' + sec : sec;
  return {
    year: y,
    month: m,
    day: d,
    date: `${m}-${d}`,
    time: `${h}:${min}`,
    normal_date: `${y}-${m}-${d}`,
    detail_time: `${y}-${m}-${d} ${h}:${min}`,
    second: sec,
  };
}

/**
 *
 * @param {秒单位的时间戳} timeStamp
 * @returns {多少秒/分钟/小时/天 前/后}
 */
Vue.prototype.$getDateDiff = getDateDiff;
export function getDateDiff(timeStamp) {
  let minute = 1000 * 60;
  let hour = minute * 60;
  let day = hour * 24;
  let month = day * 30;
  let backText = i18n.t('前');
  let result = '';

  let target = new Date(timeStamp * 1000);
  let now = new Date().getTime();
  let diffValue = now - target;
  if (diffValue < 0) {
    diffValue = -diffValue;
    backText = i18n.t('后');
  }
  let monthC = diffValue / month;
  let weekC = diffValue / (7 * day);
  let dayC = diffValue / day;
  let hourC = diffValue / hour;
  let minC = diffValue / minute;
  if (monthC >= 1) {
    result = parseInt(monthC) + i18n.t('个月') + backText;
  } else if (weekC >= 1) {
    result = parseInt(weekC) + i18n.t('周') + backText;
  } else if (dayC >= 1) {
    result = parseInt(dayC) + i18n.t('天') + backText;
  } else if (hourC >= 1) {
    result = parseInt(hourC) + i18n.t('个小时') + backText;
  } else if (minC >= 1) {
    result = parseInt(minC) + i18n.t('分钟') + backText;
  } else {
    if (backText == i18n.t('后')) {
      result = i18n.t('一分钟内');
    } else {
      result = i18n.t('刚刚');
    }
  }
  return result;
}
