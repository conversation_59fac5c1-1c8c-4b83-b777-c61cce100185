export default {
  state: {
    gameInfo: {},
    getUdidPopupShow: false, //是否显示UDID获取弹窗,
    downloadGrqDialogShow: false, //是否显示个人签下载弹窗
    grqStatus: 0, //签名状态
    grqLoading: false, //个人签loading
    noSafariShow: false, //是ios但是非safari弹窗显示
    downloadPopupShow: false, //IOS下载弹窗
    h5GameUrl: '', //当前h5游戏url
  },
  getters: {
    gameInfo(state) {
      return state.gameInfo;
    },
    getUdidPopupShow(state) {
      return state.getUdidPopupShow;
    },
    downloadGrqDialogShow(state) {
      return state.downloadGrqDialogShow;
    },
    grqStatus(state) {
      return state.grqStatus;
    },
    grqLoading(state) {
      return state.grqLoading;
    },
    noSafariShow(state) {
      return state.noSafariShow;
    },
    downloadPopupShow(state) {
      return state.downloadPopupShow;
    },
    h5GameUrl(state) {
      return state.h5GameUrl;
    },
  },
  mutations: {
    setGameInfo(state, payload) {
      state.gameInfo = Object.assign({}, payload);
    },
    setGetUdidPopupShow(state, payload) {
      state.getUdidPopupShow = payload;
    },
    setDownloadGrqDialogShow(state, payload) {
      state.downloadGrqDialogShow = payload;
    },
    setGrqStatus(state, payload) {
      state.grqStatus = payload;
    },
    setGrqLoading(state, payload) {
      state.grqLoading = payload;
    },
    setNoSafariShow(state, payload) {
      state.noSafariShow = payload;
    },
    setDownloadPopupShow(state, payload) {
      state.downloadPopupShow = payload;
    },
    setInitDownloadStatus(state) {
      state.getUdidPopupShow = false; //是否显示UDID获取弹窗,
      state.downloadGrqDialogShow = false; //是否显示个人签下载弹窗
      state.grqStatus = 0; //签名状态
      state.grqLoading = false; //个人签loading
      state.noSafariShow = false; //是ios但是非safari弹窗显示
      state.downloadPopupShow = false; //IOS下载弹窗
    },
    setH5GameUrl(state, payload) {
      state.h5GameUrl = payload;
    },
  },
};
