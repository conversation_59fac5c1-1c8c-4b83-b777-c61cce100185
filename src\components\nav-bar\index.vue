<template>
  <div
    class="nav-bar-component"
    ref="navBar"
    v-show="platform != 'android' && platform != 'ios'"
  >
    <div class="bg" :class="bgStyle">
      <van-nav-bar
        fixed
        :placeholder="placeholder"
        safe-area-inset-top
        :border="border"
      >
        <template #left>
          <slot name="left">
            <div class="back" @click="back" v-if="backShow"></div>
          </slot>
        </template>
        <template #title>
          <slot name="center">
            <div class="nav-title">{{ title }}</div>
          </slot>
        </template>
        <template #right>
          <slot name="right"></slot>
        </template>
      </van-nav-bar>
    </div>
  </div>
</template>

<script>
import { platform } from '@/utils/box.uni.js';
import { NavBar } from 'vant';
import 'vant/lib/nav-bar/style/less';
export default {
  name: 'NavBar',
  components: {
    'van-nav-bar': NavBar,
  },
  props: {
    backShow: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '',
    },
    bgStyle: {
      type: String,
      validator: function (value) {
        return [
          'green',
          'white',
          'transparent-black',
          'greenNew',
          'black',
          'transparent',
          'transparent-white',
        ].includes(value);
      },
      default: 'green',
    },
    border: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      platform: platform,
      clientHeight: 22,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.clientHeight = this.$refs.navBar.clientHeight;
    });
  },
};
</script>
<style lang="less" scoped>
.nav-bar-component {
  flex-shrink: 0;
  /deep/ .van-nav-bar--fixed {
    .fixed-center;
  }
  .green {
    /deep/ .van-nav-bar {
      background: @themeBg;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/back-white.png) center center
        no-repeat;
      background-size: 19 * @rem 15 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #ffffff;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
  .greenNew {
    /deep/ .van-nav-bar {
      background: @themeBg;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/back-white.png) center center
        no-repeat;
      background-size: 19 * @rem 15 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #ffffff;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
  .black {
    /deep/ .van-nav-bar {
      background: #1d1b23;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/back-light.png) center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #f4dfc6;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
  .white {
    /deep/ .van-nav-bar {
      background: #fff;
      .back {
        width: 30 * @rem;
        height: 50 * @rem;
        background: url(../../assets/images/back-black.png) center center
          no-repeat;
        background-size: 19 * @rem 15 * @rem;
      }
      .nav-title {
        font-size: 18 * @rem;
        color: #000;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        height: 50 * @rem;
        line-height: 50 * @rem;
      }
    }
  }
  .transparent-black {
    /deep/ .van-nav-bar {
      background: transparent;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/back-black.png) center center
        no-repeat;
      background-size: 19 * @rem 15 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #000;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
  .transparent-white {
    /deep/ .van-nav-bar {
      background: transparent;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/back-white.png) center center
        no-repeat;
      background-size: 19 * @rem 15 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #fff;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }

  .transparent {
    /deep/ .van-nav-bar {
      background: transparent;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/back-light.png) center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #f4dfc6;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
}
</style>
