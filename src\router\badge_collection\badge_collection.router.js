// 徽章模块路由
export default [
    {
        path: '/Share_Single',
        name: 'ShareSingle',
        component: () =>
            import(
        /* webpackChunkName: "badge_collection" */ '@/views/ShareBadge/ShareSingle.vue'
            ),
        meta: {
            keepAlive: true,
            pageTitle: '分享单个徽章',
        },
    },
    {
        path: '/Share_All',
        name: 'ShareAll',
        component: () =>
            import(
        /* webpackChunkName: "badge_collection" */ '@/views/ShareBadge/ShareAll.vue'
            ),
        meta: {
            keepAlive: true,
            pageTitle: '分享全部徽章',
        },
    },
];
