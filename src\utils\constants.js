import store from '@/store';
let appName = store.getters['system/appName'];
// 客服问题
export const questionList = [
  {
    id: 1,
    title: '返利问题',
    icon: 'icon1',
    data: [
      {
        q: '如何申请返利',
        a: `① 什么是游戏返利：<br>游戏返利是指当充值达到返利活动要求后，可以获得的游戏货币或道具的奖励。一般为变态版、满Vip版游戏标配活动；<br>② 如何申请返利：<br>方法一：
                      <font id="app-name-1">${appName}</font>首页，底部右下角“联系客服”申请；<br>方法二：在
                      <font id="app-name-2">${appName}</font>内搜索当前游戏，在游戏详情页里的底部右下角“联系客服”也可以申请；<br>注：道具申请可以在申请时，将道具内容填写提交给客服（部分自选道具需要注明详细道具名称）`,
      },
      {
        q: '无法申请返利',
        a: `① 请确保当前申请返利账号和游戏充值账号一致；<br>② 请核对您单日充值总额是否满足返利活动要求；<br>③ 部分游戏返利仅限充值后48小时内申请，逾期无法申请，请联系客服；`,
      },
      {
        q: '返利到账时间',
        a: `返利一般会在申请后，24小时至48小时内完成发放；<br>部分游戏周末或节假日返利会稍有延迟，如有延迟一般会在工作日尽快安排发放，无需担心；<br>注意：当日的返利申请，建议在确认总金额后再提交，避免申请后继续充值错过更高的返还奖励；<br>如若延期，确定返利依然未收到，可联系客服协助核实；`,
      },
      {
        q: '返利到账方式',
        a: `返利到账形式一般有以下几种：<br>① 发放在游戏内邮件中，注意查收领取；<br>② 直接发放在背包中，需要使用后才可获得奖励（如元宝卡、礼包等）<br>③
                      在游戏界面某个【领奖】图标中进行领取；
                      <br>④ 激活码形式发放，联系客服获取，然后在游戏内激活；<br>⑤ 直接发放到角色，需要自己留意金额道具相关变化；`,
      },
      {
        q: '道具申请方式',
        a: `有道具返还的游戏，在申请返利的时候，将要申请的道具内容一并提交给客服（部分自选道具需要注明详细道具名称）；如果已申请返利，但未备注道具，可联系客服协助；`,
      },
      {
        q: '返利未到怎么办',
        a: `① 请确认提交的申请信息完全正确，例如区服、角色、角色ID等。可在申请后关注申请是否被驳回，以及驳回原因，修正后重新提交即可；<br>②
                      请勿在返利未发放前修改游戏内角色名称，角色名称不对可能导致返利发放失败，甚至发放到其他角色；
                      <br>③ 确实未到账，请联系客服协助咨询；`,
      },
      {
        q: '返利到账不符',
        a: `一般原因如下：<br>① 检查活动日期与充值时段是否匹配，如果不是活动期间的充值，那么是无法获得对应奖励的；<br>②
                      检查返利申请时是否忘记向客服填写道具奖励，以及道具名称是否详细，或特殊返利活动比例；
                      <br>③ 其他原因导致的发放错误；<br>④ 根据活动返利比例和下方返利计算方法，核实是否计算有误；
                      <br>以上问题，如发现返利内容和应得内容不符，可及时联系客服反馈，协助补齐奖励或了解原因。<br><br>返利计算方式：【充值金额】x【返利比例】x【游戏充值比例】=最终获取元宝数量<br>举例：A游戏充值比例为1：300；您充值100元后，活动返利为10%，那么应得返利元宝：100x10%x300=3000元宝`,
      },
      {
        q: '如何查看角色ID',
        a: `不同游戏角色ID查看方法不同，但基本类型如下：<br>① 点开游戏内角色头像，可查看角色ID；<br>② 点击游戏内设置选项，可查看角色ID；<br>③ 如果没有id，可不提供`,
      },
    ],
  },
  {
    id: 2,
    title: '充值问题',
    icon: 'icon2',
    data: [
      {
        q: '充值未到账',
        a: `① 【账号登录错误】请确认登录账号和充值账号是否一致；<br>② 【未支付成功】请确认是否正常通过支付宝或微信付款成功；<br>③
                      【其他故障】支付成功，但游戏未到账，可退出游戏后重新登录查看，如果依然存在问题，请联系客服协助。（另外每笔订单支付间隔不要过短，低于一分钟的多笔支付可能存在充值异常导致无法到账）`,
      },
      {
        q: '支付限额怎么办',
        a: `当支付提示达到商户限额，可尝试切换稳定网络或4G提交，并适当降低单笔充值额度，分多次支付一般即可避免。如果游戏内或者活动有单笔充值金额要求，可选择分多次支付购买平台币，然后游戏内充值选择用平台币一次性付款。`,
      },
    ],
  },
  {
    id: 3,
    title: '代金券',
    icon: 'icon3',
    data: [
      {
        q: '充值未到账',
        a: `① 【账号登录错误】请确认登录账号和充值账号是否一致；<br>② 【未支付成功】请确认是否正常通过支付宝或微信付款成功；<br>③
                      【其他故障】支付成功，但游戏未到账，可退出游戏后重新登录查看，如果依然存在问题，请联系客服协助。（另外每笔订单支付间隔不要过短，低于一分钟的多笔支付可能存在充值异常导致无法到账）`,
      },
      {
        q: '支付限额怎么办',
        a: `当支付提示达到商户限额，可尝试切换稳定网络或4G提交，并适当降低单笔充值额度，分多次支付一般即可避免。如果游戏内或者活动有单笔充值金额要求，可选择分多次支付购买平台币，然后游戏内充值选择用平台币一次性付款。`,
      },
    ],
  },
  {
    id: 4,
    title: '账号问题',
    icon: 'icon4',
    data: [
      {
        q: '忘记账号',
        a: `账号充值过：联系客服提供任意一笔订单支付记录或支付记录截图，即可帮您查到对应账号；<br>账号未充值：联系客服提供IMEI码{在注册账号的手机中输入*#06#即可看到本机的IMEI码，刷机过或非注册账号手机IMEI码无法查询}；`,
      },
      {
        q: '忘记密码',
        a: `【有绑定手机-号码可用】，可通过忘记密码选项自助进行找回修改密码；<br>【有绑定手机-号码无用】，可向客服提供账号和支付宝、微信支付方式的历史支付记录截图，核实；如未涉及账号交易或账号共享，且充值记录核实无误则可以找回；<br>【未绑定手机-有充值过】，可向客服提供账号和支付宝、微信支付方式的历史支付记录截图，核实找回。<br>【未绑定手机-没有充值】，可向客服提供IMEI码{在注册账号的手机中输入*#06#即可看到本机的IMEI码，刷机过或非注册账号手机IMEI码无法查询}协助核实找回。`,
      },
      {
        q: '如何换绑手机号',
        a: ` ① 在
                      <font id="app-name-3">${appName}</font>内（我的--头像--手机绑定），通过当前绑定手机短信验证后即可解绑换绑；<br>②
                      如果之前绑定手机不可用，可向客服提供账号和支付宝、微信支付方式的历史支付记录截图，进行核实，未涉及账号交易或账号共享，且充值记录核实无误才可以协助换绑；`,
      },
      {
        q: '账号被盗、交易专栏',
        a: ` 郑重声明：请合理保护个人账号信息；凡线下交易、账号共享导致的账号纠纷问题，官方不予受理；<br>为什么不受理线下交易、账号共享的纠纷问题：<br>①线下交易、账号共享等产生的纠纷，没有明确的交易流程和单号证据，且聊天截图、转账截图等均可伪造，基本无法判断事实真伪，故无法根据双方说辞判断真正归属。这也是各大游戏平台对于此类问题无能为力的原因。因此建议珍惜自己的账号财产，避免损失时候求助无门。<br>②从本质意义而言，账号共享、线下交易本就是自身对个人账户财产安全的不负责。PS：一般来说，游戏账号不会莫名其妙被盗。以下三种情况最容易导致账号安全问题：使用辅助工具、账号线下交易、账号共享给其他用户导致信息被修改。`,
      },
    ],
  },
  {
    id: 5,
    title: '交易问题',
    icon: 'icon5',
    data: [
      {
        q: '购买的账号在哪里？',
        a: `购买后、角色小号自动转入你的app账号、登录游戏即可查收角色。`,
      },
      {
        q: '交易审核时间？',
        a: `提交审核后，将在3个工作日内审核上架，且小号在该游戏中所有区服角色都将被冻结，无法登录。`,
      },
      {
        q: '交易账号的累积金额？',
        a: `出售时显示累计金额为该小号在该游戏同一小号下多个区服的角色所有累计充值。`,
      },
      {
        q: '为什么我出售的账号找不到了？',
        a: `出售的角色可能会因为时间关系导致游戏内排行、数据等发生变化，或者一些较老的游戏关服等情况，导致出现买卖纠纷。`,
      },
      {
        q: '交易账号所得在哪查看？',
        a: `交易账号所得为平台币（平台币不支持提现）并直接发送至账号中，进入【我的】— 【平台币】— 【平台币明细】中可查看。`,
      },
      {
        q: '平台币不能购买交易账号',
        a: `由于平台币的特殊性，因此平台币无法用于购买账号，敬请见谅。`,
      },
    ],
  },
  {
    id: 6,
    title: '平台币/金币问题',
    icon: 'icon6',
    data: [
      {
        q: '购买的账号在哪里？',
        a: `购买后、角色小号自动转入你的app账号、登录游戏即可查收角色。`,
      },
      {
        q: '交易审核时间？',
        a: `提交审核后，将在3个工作日内审核上架，且小号在该游戏中所有区服角色都将被冻结，无法登录。`,
      },
      {
        q: '交易账号的累积金额？',
        a: `出售时显示累计金额为该小号在该游戏同一小号下多个区服的角色所有累计充值。`,
      },
      {
        q: '为什么我出售的账号找不到了？',
        a: `出售的角色可能会因为时间关系导致游戏内排行、数据等发生变化，或者一些较老的游戏关服等情况，导致出现买卖纠纷。`,
      },
      {
        q: '交易账号所得在哪查看？',
        a: `交易账号所得为平台币（平台币不支持提现）并直接发送至账号中，进入【我的】— 【平台币】— 【平台币明细】中可查看。`,
      },
      {
        q: '平台币不能购买交易账号',
        a: `由于平台币的特殊性，因此平台币无法用于购买账号，敬请见谅。`,
      },
    ],
  },
  {
    id: 7,
    title: '游戏相关问题',
    icon: 'icon7',
    data: [
      {
        q: '下载登录异常',
        a: `【下载很慢】<br>①请检查网络是否畅通，尝试切换网络（3G/4G/wifi）再下载；<br>②请检查手机存储空间是否充足；<br>如果依然无法下载，请联系客服咨询；<br>【登录异常】<br>①无法进入游戏--请检查当前游戏是否是维护中，如服务器正常可联系客服咨询；<br>②无限登录账号--联系客服核实是否为服务器故障；<br>③卡更新进度条--切换网络尝试或检查存储空间是否充足；<br>【长时间未登录进入保护状态】<br>保护状态是对用户账号安全的一种自动保护设置；遇到此类问题可直接联系客服协助恢复即可正常登录；`,
      },
      {
        q: '游戏内悬浮球',
        a: `如何开启游戏内悬浮球：<br>方法1：手机【设置】--【应用管理】--当前游戏名称--【权限管理】--悬浮窗打勾允许<br>方法2：手机【手机管家】--【权限管理】--当前游戏名称--悬浮窗打勾允许`,
      },
      {
        q: '电脑如何玩手游',
        a: `① 在电脑上安装安卓模拟器，可自行搜索选择下载，比如海马模拟器、夜神模拟器等；<br>② 在电脑上搜索下载
                      <font id="app-name-4">${appName}</font>安装包/或直接搜索当前游戏安装包下载；<br>③ 打开模拟器，将此安装包拖进模拟器安装，即可正常使用。`,
      },
      {
        q: 'IOS设置信任',
        a: `由于ios9系统限制，游戏安装完成后，首次打开会提示“未受信任的企业级开发者”。<br>设置信任操作步骤如下：<br>【设置】--【通用】--【设备管理】--【文件描述】--【信任文件】`,
      },
    ],
  },
];
