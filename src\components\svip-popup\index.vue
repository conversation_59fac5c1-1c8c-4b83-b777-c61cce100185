<template>
  <van-dialog
    v-model="isShowSvipPopup"
    :showConfirmButton="false"
    :lockScroll="false"
    class="svip-popup"
  >
    <div class="container" @click="goSvip">
      <img class="bg" :src="initData.svip_img" alt="" />
      <!-- <img class="bg" src="@/assets/images/home/<USER>" alt="" />
      <div class="content">
        <img class="bg2" src="@/assets/images/home/<USER>" alt="" />
        <img
          class="svip-welfare"
          src="@/assets/images/home/<USER>"
          alt=""
        />
        <div class="welfare-list">
          <div class="welfare-item">
            <div class="welfare-text">2280平台币</div>
            <div class="welfare-tip">（最高领取）</div>
          </div>
          <div class="welfare-item">
            <div class="welfare-text">7550金币</div>
            <div class="welfare-tip">（最高领取）</div>
          </div>
          <div class="welfare-item">
            <div class="welfare-text">SVIP折扣券</div>
            <div class="welfare-tip">（最低1折）</div>
          </div>
        </div>
      </div>
      <div class="popup-tip">开通解锁更多SVIP特权</div>
      <div class="go-svip btn" @click="goSvip">立即开通</div> -->
    </div>
    <div class="close" @click="$emit('update:isShowSvipPopup', false)"></div>
  </van-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    isShowSvipPopup: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },

  methods: {
    goSvip() {
      this.$emit('update:isShowSvipPopup', false);
      this.$nextTick(() => {
        this.toPage('Svip');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.svip-popup {
  background-color: rgba(0, 0, 0, 0);
  width: 309 * @rem;
  .container {
    width: 309 * @rem;
    height: 424 * @rem;
    position: relative;
    overflow: hidden;
    .bg {
      width: 309 * @rem;
      height: 424 * @rem;
      position: absolute;
      left: 0;
      top: 0;
    }
    .content {
      height: 185 * @rem;
      margin: 145 * @rem auto 0;
      position: relative;
      overflow: hidden;
      .bg2 {
        width: 215 * @rem;
        height: 185 * @rem;
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
      }
      .svip-welfare {
        position: relative;
        width: 185 * @rem;
        height: 68 * @rem;
        margin: 69 * @rem auto 0;
      }
      .welfare-list {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 5 * @rem;
        .welfare-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 6 * @rem;
          .welfare-text {
            font-size: 11 * @rem;
            color: #273f73;
          }
          .welfare-tip {
            font-size: 8 * @rem;
            color: #273f73;
            margin-top: 2 * @rem;
          }
        }
      }
    }
    .popup-tip {
      font-size: 11 * @rem;
      color: #90b0c1;
      text-align: center;
      position: relative;
    }
    .go-svip {
      width: 183 * @rem;
      height: 48 * @rem;
      background: url(~@/assets/images/home/<USER>
      background-size: 183 * @rem 48 * @rem;
      margin: 10 * @rem auto 0;
      display: flex;
      justify-content: center;
      font-size: 16 * @rem;
      line-height: 44 * @rem;
      color: #ffffff;
      font-weight: 600;
    }
  }
  .close {
    width: 26 * @rem;
    height: 26 * @rem;
    background-image: url(~@/assets/images/close-filled.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 25 * @rem auto 0;
  }
}
</style>
