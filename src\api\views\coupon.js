import { request } from '../index';

/**
 * 代金券center
 * @param orderType 3=热门(按领取数)，2=最新上架（时间倒序），1=最高折扣(按折扣金额倒序)
 */
export function ApiCouponList(params = {}) {
  return request('/api/coupon/couponList', params);
}

/**
 *  一个游戏的代金券总数量和总money的列表
 * @param orderType 3=热门(按领取数)，2=最新上架（时间倒序），1=最高折扣(按折扣金额倒序)
 */
export function ApiCouponGameCoupons(params = {}) {
  return request('/api/coupon/gameCoupons', params);
}
/**
 * 单游戏代金券
 * @param gameId
 * @param orderType 3=热门(按领取数)，2=最新上架（时间倒序），1=最高折扣(按折扣金额倒序)
 */
export function ApiCouponCoupon(params = {}) {
  return request('/api/coupon/coupon', params);
}

/**
 * 领取代金券
 * @param couponId  代金券id
 */
export function ApiCouponTake(params = {}) {
  return request('/api/coupon/take', params);
}

/**
 * 判断小号是否可领取代金券
 * @param couponId  代金券id
 * @param gameId  游戏id
 * @param xhId  小号id
 */
export function ApiCouponCheckCoupon(params = {}) {
  return request('/api/coupon/checkCoupon', params);
}

/**
 * 我的代金券
 * @param state 显示状态：0：可使用,2:已使用，1：已过期,
 * @param gameId
 */
export function ApiCouponMine(params = {}) {
  return request('/api/coupon/mine', params);
}

/**
 * 是否领取无门槛券
 * @return {is_receive} 是否已领取
 */
export function ApiCouponIsReceiveFirstC(params = {}) {
  return request('/api/coupon/isReceiveFirstC', params);
}

/**
 * 获取无门槛券信息  首充福利信息接口
 */
export function ApiCouponGetFirstCouponAjax(params = {}) {
  return request('/web/coupon/getFirstCouponAjax', params);
}

/**
 * 获取无门槛券信息 领取接口
 * coupon_id 券ID
 */
export function ApiCouponTakeCouponAjax(params = {}) {
  return request('/web/coupon/takeCouponAjax', params);
}

/**
 * 获取代金券列表(游戏中)
 * orderId 订单ID
###
#return
#    //不可使用
#    "no_list": [],
#    //可使用
#    "list": [
#      {
#        "id": 23718584,  //代金券记录  id
#        "type": 0,  //代金券类型：1-svip，0-普通 2首充 3转游 4平台币券
#        "money": 6,  //代金券金额
#        "reach_money": 6, //达标使用金额
#        "period": 1, //领取后有效期：天数
#        "game_id": 0, //关联游戏id
#        "create_time": "2021.10.20",
#        "expire_time": "2021.10.20", //到期时间
#        "title": "通用代金券",
#        "remark": "3733任意游戏充值使用"
#      }
#    ]
*/
export function ApiGetGameCouponList(params = {}) {
  return request('/pay/api/getCouponList', params, false);
}

/**
 * orderId 订单ID
 * RecordId 优惠券id
 */
export function ApiChooseCoupon(params = {}) {
  return request('/pay/api/chooseCoupon', params, false);
}

/**
 * orderId 订单ID
 * RecordId 优惠券id
 */
export function ApiTakeByFirstCoupon(params = {}) {
  return request('/web/coupon/takeByFirstCoupon', params);
}
