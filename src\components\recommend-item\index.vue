<template>
  <div class="recommend-item">
    <div
      class="game-top"
      @click="toPage('GameDetail', { id: info.id, gameInfo: info })"
    >
      <div class="game-icon">
        <div v-if="subscript" class="subscript">{{ subscript }}</div>
        <img :src="info.titlepic" alt="" />
      </div>
      <div class="game-right">
        <div class="game-name">
          {{ info.main_title
          }}<span class="game-subtitle" v-if="info.subtitle">{{
            info.subtitle
          }}</span>
        </div>
        <div class="game-bottom">
          <div class="score">
            {{ info.rating ? info.rating.rating : '10' }}{{ $t('分') }}
          </div>
          <div class="types">
            <template v-for="(type, typeIndex) in info.type">
              <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
                type
              }}</span>
            </template>
          </div>
          <div class="server-date" v-if="info.service_date">
            {{ info.service_date }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="pic-container"
      v-if="morePicFirst"
      @click="toPage('GameDetail', { id: info.id, gameInfo: info })"
    >
      <template v-for="(item, index) in info.morepic.small">
        <img
          :src="item"
          alt=""
          :key="index"
          v-if="index < 3"
          @click="showBigImage(info.morepic.big, index)"
        />
      </template>
    </div>
    <div class="video-container" @click="play(index)" v-else>
      <video
        :src="info.video_url"
        :poster="info.video_thumb"
        :webkit-playsinline="true"
        :playsinline="true"
        :x5-playsinline="true"
        x-webkit-airplay="allow"
      >
        {{ $t('您的手机不支持该视频文件！！！') }}
      </video>
      <div v-if="info.video_url" class="play"></div>
    </div>
    <div class="game-smalltext">{{ info.yxftitle }}</div>
  </div>
</template>

<script>
export default {
  name: 'recommendItem',
  props: {
    info: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      default: 0,
    },
    // 优先显示多截图
    morePicFirst: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 游戏角标
    subscript() {
      if (this.info.is_subsidy) {
        return this.$t('百亿补贴');
      } else if (
        this.info.classid == 107 &&
        this.info.f_pay_rebate &&
        this.info.f_pay_rebate != 0 &&
        this.info.f_pay_rebate != 100
      ) {
        return `${(parseFloat(this.info.f_pay_rebate) / 10).toFixed(
          1,
        )}${this.$t('折')}`;
      } else if (
        this.info.classid == 107 &&
        this.info.pay_rebate &&
        this.info.pay_rebate != 100
      ) {
        if (parseFloat(this.info.pay_rebate) == 100) {
          return `10${this.$t('折')}`;
        } else {
          return `${(parseFloat(this.info.pay_rebate) / 10).toFixed(1)}折`;
        }
      } else if (this.info.first_pay_icon) {
        return this.$t('无门槛');
      } else if (this.info.has_coupon) {
        return this.$t('代金券');
      } else {
        return false;
      }
    },
  },
  methods: {
    play(index) {
      if (!this.info.video_url) {
        this.toPage('GameDetail', { id: this.info.id, gameInfo: this.info });
        return false;
      }
      this.pauseAll();
      let $video = document.querySelector(
        `.recommend-item:nth-of-type(${index + 1}) video`,
      );
      let $play = document.querySelector(
        `.recommend-item:nth-of-type(${index + 1}) .play`,
      );
      $play.style.display = 'none';
      $video.play();
      $video.setAttribute('controls', 'true');
    },
    pauseAll() {
      let $videos = document.querySelectorAll('.recommend-item video');
      let $plays = document.querySelectorAll('.recommend-item .play');
      $videos.forEach(item => {
        item.pause();
        item.removeAttribute('controls');
      });
      $plays.forEach(item => {
        item.style.display = 'block';
      });
    },
  },
};
</script>

<style lang="less" scoped>
.recommend-item {
  box-sizing: border-box;
  width: 339 * @rem;
  background: #ffffff;
  box-shadow: 0 * @rem 3 * @rem 11 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
  border-radius: 10 * @rem;
  margin: 0 auto;
  margin-top: 10 * @rem;
  padding: 13 * @rem;
  .game-top {
    display: flex;
    align-items: center;
    padding-bottom: 10 * @rem;
    .game-icon {
      width: 40 * @rem;
      height: 40 * @rem;
      position: relative;
      background-color: #eeeeee;
      .subscript {
        height: 16 * @rem;
        display: flex;
        align-items: center;
        position: absolute;
        top: -5 * @rem;
        right: -8 * @rem;
        padding: 0 5 * @rem;
        background: rgba(255, 117, 84, 1);
        font-size: 12 * @rem;
        color: #fff;
        border-radius: 10 * @rem 10 * @rem 10 * @rem 0;
        transform: scale(0.75);
        transform-origin: right top;
        border: 0.5 * @rem solid #ebebeb;
      }
    }
    .game-right {
      margin-left: 10 * @rem;
      flex: 1;
      min-width: 0;
      .game-name {
        font-size: 15 * @rem;
        color: #383838;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        .game-subtitle {
          box-sizing: border-box;
          border: 1 * @rem solid #ff9146;
          border-radius: 3 * @rem;
          font-size: 11 * @rem;
          padding: 2 * @rem 3 * @rem;
          color: #fe6600;
          margin-left: 5 * @rem;
          vertical-align: middle;
          line-height: 1;
        }
      }
      .game-bottom {
        flex: 1;
        min-width: 0;
        font-size: 12 * @rem;
        color: #929292;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 4 * @rem;
        .score {
          height: 17 * @rem;
          display: flex;
          align-items: center;
        }

        .types {
          display: flex;
          align-items: center;
          margin-left: 8 * @rem;
          height: 17 * @rem;
          flex: 1;
          min-width: 0;
          .type {
            padding: 0 5 * @rem;
            position: relative;
            display: flex;
            align-items: center;
            &:not(:first-child) {
              &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 1 * @rem;
                height: 10 * @rem;
                background-color: #929292;
              }
            }
          }
        }
        .server-date {
          width: 65 * @rem;
          height: 17 * @rem;
          background: linear-gradient(90deg, #ff7575 0%, #fe6600 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: #ffffff;
          font-weight: 400;
          border-radius: 12 * @rem;
        }
      }
    }
  }

  .pic-container {
    width: 313 * @rem;
    max-height: 176 * @rem;
    position: relative;
    margin: 3 * @rem auto 0;
    border-radius: 8 * @rem;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    img {
      width: 100 * @rem;
      border-radius: 8 * @rem;
    }
  }
  .video-container {
    width: 313 * @rem;
    height: 176 * @rem;
    position: relative;
    margin: 0 auto;
    border-radius: 8 * @rem;
    overflow: hidden;
    video {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 45 * @rem;
      height: 45 * @rem;
      background-image: url(~@/assets/images/video-play.png);
      background-size: 100%;
    }
  }
  .game-smalltext {
    font-size: 13 * @rem;
    color: #797979;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 10 * @rem;
  }
}
</style>
