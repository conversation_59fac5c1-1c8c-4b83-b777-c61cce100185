import { request } from '../index';

export function ApiXiaohaoMyListByCard(params = {}) {
  return request('/api/xiaohao/myListByCard', params);
}

/**
 * 小号交易列表
 * @param isDone 是否已成交： 1是 0否
 * @param order 1=最新发布，2=价格最低，3=价格最高
 * @param deviceFrom 平台：0=全部，11=安卓，12=iOS
 * @param gameId 123
 * @param cateType 游戏类型
 * @param tradeId 可选，排除此交易
 * @param page 1
 * @param listRows 10
 */
export function ApiXiaohaoTradeList(params = {}) {
  return request('/api/xiaohao/tradeList', params);
}

/**
 * 交易详情
 * @param tradeId
 */
export function ApiXiaohaoTradeItem(params = {}) {
  return request('/api/xiaohao/tradeItem', params);
}

/**
 * 交易详情
 */
export function ApiGameCateForTrade(params = {}) {
  return request('/api/game/cateForTrade', params);
}

/**
 * 小号历史
 */
export function ApiXiaohaoTradeHistory(params = {}) {
  return request('/api/xiaohao/tradeHistory', params);
}

/**
 * 我的出售列表
 * @param status -1=显示全部，0 9=审核中，6=出售中，7=已出售,4=已下架
 * @param page
 * @param listRows
 */
export function ApiXiaohaoMyTradeList(params = {}) {
  return request('/api/xiaohao/myTradeList', params);
}

/**
 * 小号交易_我的订单
 * @param status -1=显示全部，0=待支付，1=支付成功，2=已取消
 * @param page
 * @param listRows
 */
export function ApiXiaohaoMyOrderList(params = {}) {
  return request('/api/xiaohao/myOrderList', params);
}

/**
 * 小号捡漏_我的订单
 * @param status -1=显示全部，0=待支付，1=支付成功，2=已取消
 * @param page
 * @param listRows
 */
export function ApiXiaohaoMyOrderListJL(params = {}) {
  return request('/api/xiaohao/myOrderListJL', params);
}

/**
 * 卖号流程 玩过游戏列表
 * @param keyword
 */
export function ApiXiaohaoGetGameList(params = {}) {
  return request('/api/xiaohao/getGameList', params);
}

/**
 * 卖号流程 根据游戏获取小号列表
 * @param appId
 */
export function ApiXiaohaoGetXhList(params = {}) {
  return request('/api/xiaohao/getXhList', params);
}

/**
 * 查询小号充值总额
 * @param xhId 小号id
 * @param type 1=卖号，2=回收
 */
export function ApiXiaohaoPaySum(params = {}) {
  return request('/api/xiaohao/paySum', params);
}

/**
 * 发布小号交易
 * @param xhId 小号id
 * @param price 价格
 * @param smsCode 短信验证码
 * @param gameArea 主要区服
 * @param secret 游戏内二级密码（选填）
 * @param roleName 主要角色名称（选填）
 * @param desc 描述 （选填）
 * @param images 游戏截图 json格式
 * @param videoUrl 游戏视频 （选填）
 * @param specifyUser 指定用户名（选填）
 */
export function ApiXiaohaoCreateTrade(params = {}) {
  return request('/api/xiaohao/createTrade', params);
}

/**
 * 发布小号交易（重新编辑）
 * @param tradeId 交易id
 * @param price 价格
 * @param smsCode 短信验证码
 * @param gameArea 主要区服
 * @param secret 游戏内二级密码（选填）
 * @param roleName 主要角色名称（选填）
 * @param desc 描述 （选填）
 * @param images 游戏截图 json格式
 * @param videoUrl 游戏视频 （选填）
 * @param specifyUser 指定用户名（选填）
 */
export function ApiXiaohaoEditTrade(params = {}) {
  return request('/api/xiaohao/editTrade', params);
}

/**
 * 发布小号交易
 * @param status   3=上架，4=下架，5=取消，100 = 删除
 */
export function ApiXiaohaoChangeTradeStatus(params = {}) {
  return request('/api/xiaohao/changeTradeStatus', params);
}

/**
 * 卖号流程 验证指定用户名（废弃，用下面那个，坑死了）
 * @param specifyUser   指定用户名
 */
export function ApiXiaohaoCheckSpecifyUser(params = {}) {
  return request('/api/xiaohao/checkSpecifyUser', params);
}

/**
 * 卖号流程 验证指定用户名(在接口user文件里~_~)
 * @param username 指定用户名
 */
export function ApiUserCheckUserInfo(params = {}) {
  return request('/api/user/checkUserInfo', params);
}

/**
 * 创建小号
 * appid
 * nickname
 */
export function ApiXiaohaoCreate(params = {}) {
  return request('/api/xiaohao/create', params);
}

/**
 * 购买小号_下单
 * @param ssId   快照id
 * @param price   价格，整数
 */
export function ApiXiaohaoCreateOrder(params = {}) {
  return request('/api/xiaohao/createOrder', params);
}

/**
 * 小号捡漏_下单
 * @param recycleId   快照id
 * @param rmb   价格，整数
 */
export function ApiXiaohaoCreateOrderJL(params = {}) {
  return request('/api/xiaohao/createOrderJL', params);
}

/**
 * 购买小号_下单
 * @param orderId
 * @param orderType   201=小号交易，202=小号捡漏
 */
export function ApiXiaohaoPayUrl(params = {}) {
  return request('/api/xiaohao/payUrl', params);
}

/**
 * 修改交易价格（不用重新进入审核）
 * @param ssId  列表中得ss_id
 * @param price  价格，整数
 * @param smsCode  验证码
 */
export function ApiXiaohaoEditTradePrice(params = {}) {
  return request('/api/xiaohao/editTradePrice', params);
}

/**
 * 修改订单状态_删除订单
 * @param status  2 = 已取消 100 删除
 * @param orderId
 */
export function ApiXiaohaoChangeOrderStatus(params = {}) {
  return request('/api/xiaohao/changeOrderStatus', params);
}

/**
 * 小号捡漏_修改订单状态_删除订单
 * @param status  2 = 已取消 100 删除
 * @param orderId
 */
export function ApiXiaohaoChangeOrderStatusJL(params = {}) {
  return request('/api/xiaohao/changeOrderStatusJL', params);
}

/**
 * 买号列表 -- 游戏搜索
 * @param keyword
 */
export function ApiGameTitleHints(params = {}) {
  return request('/api/game/titleHints', params);
}

/**
 * 小号捡漏_列表
 * @param isDone 0 是否已成交(已成交时order无效，固定以最新成交时间排序)
 * @param order 1=最新上架(默认)，2=价格最低，3=价格最高，4=创号最短(小号id最大)，5=创号最久(小号id最小)
 * @param deviceFrom 0=全部平台，11=安卓，12=iOS
 * @param cateType 游戏类型id，由接口api/game/cate获得
 * @param priceId 0=全部，其余以返回参数 price_range 为准
 * @param appId 过滤项，游戏id，两种均支持，appId优先
 * @param gameId
 * @param recycleId 排除此单项，主要用于 相关商品
 * @param noAbout 1、用户选择不再提示后该参数客户端用1请求，2、否则APP每次启动第一次为0，后续为1
 */
export function ApiXiaohaoJianlouList(params = {}) {
  return request('/api/xiaohao/jianlouList', params);
}

/**
 * 小号捡漏_详情
 * @param recycleId
 */
export function ApiXiaohaoJianlouItem(params = {}) {
  return request('/api/xiaohao/jianlouItem', params);
}

/**
 * 小号游戏列表 视频
 * @param xhId 小号id
 */
export function ApiXiaohaoGetXiaoHaoVideo(params = {}) {
  return request('/api/xiaohao/getXiaoHaoVideo', params);
}

/**
 * 所有有消费的小号列表
 * @param keyword 游戏名或小号昵称关键字
 * @param type 1=卖号，2=回收,3=捡漏
 */
export function ApiXiaohaoAllPayerList(params = {}) {
  return request('/api/xiaohao/allPayerList', params);
}

/**
 * 提交回收小号
 * @param xhId 小号ID
 * @param recyclePtb 回收平台币数，用于验证
 * @param smsCode 短信验证码
 * @param gameArea 主要区服
 * @param secret 游戏内二级密码（选填）
 * @param roleName 主要角色名称（选填）
 */
export function ApiXiaohaoRecycle(params = {}) {
  return request('/api/xiaohao/recycle', params);
}

/**
 * 我提交的回收记录
 */
export function ApiXiaohaoMyRecycleList(params = {}) {
  return request('/api/xiaohao/myRecycleList', params);
}

/**
 * 购回回收小号
 * @param recycleId 回收ID
 * @param redeemPrice 购回需要的平台币数，用于验证
 */
export function ApiXiaohaoRedeem(params = {}) {
  return request('/api/xiaohao/redeem', params);
}

/**
 * 删除回收记录
 * @param recycleId 回收ID
 * @param status 100 = 删除
 */
export function ApiXiaohaoChangeRecycleStatus(params = {}) {
  return request('/api/xiaohao/changeRecycleStatus', params);
}

/**
 * 小号管理
 * @param keyword '主宰'
 * @param appId 游戏ID 类型为2 按游戏搜索 获取游戏小号列表
 */
export function ApiXiaohaoManage(params = {}) {
  return request('/api/xiaohao/manage', params);
}

/**
 * 小号管理 - 按游戏
 * @param keyword 输入小号id、小号昵称、游戏名
 */
export function ApiXiaohaoManageGame(params = {}) {
  return request('/api/xiaohao/manageGame', params);
}

/**
 * 修改小号昵称
 * @param nickname 小号主宰
 * @param xhId 11512720
 */
export function ApiXiaohaoChangeNickName(params = {}) {
  return request('/api/xiaohao/changeNickName', params);
}

/**
 * 我的小号列表
 * @param gameId 游戏id
 */
export function ApiXiaohaoMyListByGameId(params = {}) {
  return request('/api/xiaohao/myListByGameId', params);
}

// 砍价
/**
 * 砍价列表
 * @param tradeId 交易列表id
 */
export function ApiXiaohaoBargainList(params = {}) {
  return request('/api/xiaohao/bargainList', params);
}

/**
 * 指定
 * @param tradeId 交易列表id
 * @param bargainId 砍价列表id
 */
export function ApiXiaohaoAppointUser(params = {}) {
  return request('/api/xiaohao/appointUser', params);
}

/**
 * 砍价提交
 * @param amount 24 出价金额
 * @param tradeId 砍价列表id
 */
export function ApiXiaohaoSubmitBargain(params = {}) {
  return request('/api/xiaohao/submitBargain', params);
}

/**
 * 砍价-撤销
 * @param tradeId 交易列表id
 * @param bargainId 砍价列表id
 */
export function ApiXiaohaoCancelBargain(params = {}) {
  return request('/api/xiaohao/cancelBargain', params);
}

/**
 * 我的交易 -砍价
 * @param type 1:我的出售 2：我的出价
 */
export function ApiXiaohaoMyBargain(params = {}) {
  return request('/api/xiaohao/myBargain', params);
}

/**
 * 检测是否符合砍价要求
 * @param tradeId 交易列表id
 */
export function ApiXiaohaoCheckBargain(params = {}) {
  return request('/api/xiaohao/checkBargain', params);
}

/**
 * 交易卖号限制金额
 * @return {
 * "trade_pay_percentage": 35, //售价限制百分比
 * "trade_price_text": "价格不可低于实际充值的35%", //售价文案
 * "min_pay_sum_trade": 100//交易最低需充值金额
 * }
 */
export function ApiXiaohaoTradeIndex(params = {}) {
  return request('/api/xiaohao/tradeIndex', params);
}

/**
 * 开局号 - 获取分类
 */
export function ApiXiaohaoGetOpeningAccountCate(params = {}) {
  return request('/api/xiaohao/getOpeningAccountCate', params);
}

/**
 * 开局号 - 获取账号列表
 */
export function ApiXiaohaoGetOpeningAccount(params = {}) {
  return request('/api/xiaohao/getOpeningAccount', params);
}

/**
 * 开局号 - 购买开局号
 */
export function ApiXiaohaoPurchaseOpeningAccount(params = {}) {
  return request('/api/xiaohao/purchaseOpeningAccount', params);
}

/**
 * 开局号 - 购买记录
 */
export function ApiXiaohaoGetOpeningAccountLog(params = {}) {
  return request('/api/xiaohao/getOpeningAccountLog', params);
}
