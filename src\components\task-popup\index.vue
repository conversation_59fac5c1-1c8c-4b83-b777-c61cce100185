<template>
  <van-dialog
    v-model="taskPopup"
    :show-confirm-button="false"
    class="task-popup"
    :close-on-click-overlay="false"
    :lock-scroll="false"
  >
    <div class="popup-close" @click="closePopup"></div>
    <div class="title">
      <div class="title-text">{{ $t('任务提示') }}</div>
    </div>
    <div class="message">{{ message }}</div>
    <div class="tip" v-if="tip">{{ tip }}</div>
    <div class="confirm-btn btn" @click="handleConfirm">{{ confirmText }}</div>
  </van-dialog>
</template>

<script>
export default {
  name: 'task-popup',
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    message: {
      type: String,
      require: true,
    },
    tip: {
      type: String,
    },
    confirmText: {
      type: String,
      default() {
        return this.$t('充值平台币');
      },
    },
  },
  computed: {
    taskPopup: {
      get() {
        return this.isShow;
      },
      set(value) {
        this.$emit('update:isShow', value);
      },
    },
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm');
    },
    closePopup() {
      this.$emit('update:isShow', false);
    },
  },
};
</script>
<style lang="less" scoped>
.task-popup {
  box-sizing: border-box;
  width: 278 * @rem;
  padding: 0 20 * @rem 20 * @rem;
  .popup-close {
    position: absolute;
    right: 14 * @rem;
    top: 14 * @rem;
    width: 15 * @rem;
    height: 15 * @rem;
    .image-bg('~@/assets/images/welfare/popup-close.png');
  }
  .title {
    font-size: 18 * @rem;
    color: #000000;
    font-weight: 500;
    text-align: center;
    padding: 15 * @rem 0;
  }
  .message {
    font-size: 14 * @rem;
    color: #333;
    line-height: 20 * @rem;
    text-align: justify;
    font-weight: 400;
  }
  .tip {
    text-align: center;
    font-size: 12 * @rem;
    color: #666;
    margin-top: 10 * @rem;
    font-weight: 400;
  }
  .confirm-btn {
    width: 208 * @rem;
    height: 40 * @rem;
    background: linear-gradient(96deg, #ff9f00 0%, #fe6600 100%);
    border-radius: 20 * @rem;
    margin: 20 * @rem auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15 * @rem;
    color: #ffffff;
    font-weight: 400;
  }
}
</style>
