// 旧:
const GAME_DETAIL = 1000; //需要传extra_id(游戏id)
// const GAME_LIST      = 1002;//需要传extra_id(游戏tag),text1(游戏类型名)
// const GAME_GIFT_LIST = 1004;//礼包列表
const WEB = 1005; //需要传web_url
// const HOT_ACTIVITY   = 1006;//热门活动
// const SPEEDUP        = 1009;//加速版
// const DYNAMIC        = 1010;//动态
// const SUBSCRIBE      = 1011;//新游预约
// const EXCHANGE       = 1015;//限时兑换
// const MISSION        = 1016;//赚金任务
// const TRADE          = 1017;//账号交易
const FUN_CARD = 1018; //畅玩卡
// const MOD_GAME       = 1019;//MOD游戏
// const RECOMMEND      = 1020;//推荐
// const VIDEO          = 1021;//视频
const JIANLOU = 1022; //捡漏
const CONPON = 1023; //领券中心
const PTB_RECHARGE = 1024; //平台币充值
// const OPEN_SERVICE = 1025; //开服列表
const NEW_GAME = 1026; //新游首发
// const RANKING        = 1027;//排行榜
const CARD_HUB = 1028; //礼包中心
const GOLD_TURNTABLE = 1029; //大转盘
const ZERO_GAME = 1030; //0元畅玩
const ZHUANYOU = 1031; // 转游中心
const GOLD_MALL = 1032; // 金币商城
const GOLD_GAMBLE = 1033; // 金币夺宝
const GAME_COLLECT = 1034; // 游戏合集
const UP = 1035; // UP资源
const Recycle = 1048; // 小号回收

// 新
// const AC_ZXW                  = 1;//在线玩
// const AC_NOT_ROOT             = 2;//免ROOT
// const AC_GAME                 = 3;//游戏碎片二级页面
// const AC_ZXW_XY               = 4;//在线玩-新游
// const AC_ZXW_BD               = 5;//在线玩-榜单
// const AC_ZXW_FL               = 6;//在线玩-福利
// const AC_ZXW_LB               = 7;//在线玩-礼包
// const AC_ZJW                  = 8;//在线玩-最近玩过
// const AC_ONLINE_NEW_SUBSCRIBE = 9;//网游版-新游预约
// const AC_ONLINE_LB            = 10;//网游版-礼包
// const AC_ONLINE_LQ            = 11;//网游版-领券
// const AC_INDEX_CATE           = 12;//首页-分类  //extra_id 首页tab下标
// const AC_HALL_OF_FAME         = 13;//跳转地址全屏
// const AC_ZORE_GAME            = 14;//福利中心0元首充
// const AC_CLOSED_BETA          = 15;//内测招募
// const AC_TRIAL_PLAY           = 16;//试玩
// const AC_SANDBOX              = 17;//沙盒
// const QQ_GAME                 = 18;//QQ小游戏
// const JPYX_GAME               = 19;//精品小游戏
const CARD648 = 20; //648礼包
const OPENING_ACCOUNT = 21; //开局号
const BOUNTY_TASK = 22; //赏金任务
const GAME_SIGN_IN = 23; //游戏签到
const GAME_SIGN_IN_MORE = 24; //游戏签到更多

export const PageName = {
  [FUN_CARD]: 'ChangwanCard',
  [NEW_GAME]: 'NewGame',
  [ZHUANYOU]: 'Zhuanyou',
  [GOLD_TURNTABLE]: 'TurnTable',
  [ZERO_GAME]: 'FreePlay',
  [JIANLOU]: 'Jianlou',
  [CONPON]: 'CouponCenter',
  [PTB_RECHARGE]: 'PlatformCoin',
  [GOLD_MALL]: 'GoldMall',
  [CARD_HUB]: 'GiftCenter',
  [GOLD_GAMBLE]: 'GoldGamble',
  [GAME_DETAIL]: 'GameDetail',
  [WEB]: 'Activity',
  [GAME_COLLECT]: 'GameCollect',
  [UP]: 'Up',
  [Recycle]: 'Recycle',

  [CARD648]: 'Welfare648',
  [OPENING_ACCOUNT]: 'OpeningAccount',
  [BOUNTY_TASK]: 'BountyTask',
  [GAME_SIGN_IN]: 'SignInDetail',
  [GAME_SIGN_IN_MORE]: 'SignInList',
};

/**
 * 以下是服务端完整的actionCode，还在持续增加中 (主要是给原生安卓用的，所以有部分actionCode在web端是不存在页面的)
 * update_date: 2023年11月2日11:46:06
 */

// const AC_ZXW                  = 1;//在线玩
// const AC_NOT_ROOT             = 2;//免ROOT
// const AC_GAME                 = 3;//游戏碎片二级页面
// const AC_ZXW_XY               = 4;//在线玩-新游
// const AC_ZXW_BD               = 5;//在线玩-榜单
// const AC_ZXW_FL               = 6;//在线玩-福利
// const AC_ZXW_LB               = 7;//在线玩-礼包
// const AC_ZJW                  = 8;//在线玩-最近玩过
// const AC_ONLINE_NEW_SUBSCRIBE = 9;//网游版-新游预约
// const AC_ONLINE_LB            = 10;//网游版-礼包
// const AC_ONLINE_LQ            = 11;//网游版-领券
// const AC_INDEX_CATE           = 12;//首页-分类  //extra_id 首页tab下标
// const AC_HALL_OF_FAME         = 13;//跳转地址全屏
// const AC_ZORE_GAME            = 14;//福利中心0元首充
// const AC_CLOSED_BETA          = 15;//内测招募
// const AC_TRIAL_PLAY           = 16;//试玩
// const AC_SANDBOX              = 17;//沙盒
// const QQ_GAME                 = 18;//QQ小游戏
// const JPYX_GAME               = 19;//精品小游戏
// const CARD648                 = 20;//648礼包
// const OPENING_ACCOUNT         = 21;//开局号
// const BOUNTY_TASK             = 22;//赏金任务
// const GAME_SIGN_IN            = 23;//游戏签到
// const GAME_SIGN_IN_MORE       = 24;//游戏签到更多

// const GAME_DETAIL     = 1000;//需要传extra_id(游戏id)
// const GAME_LIST       = 1002;//需要传extra_id(游戏tag),text1(游戏类型名)
// const GAME_GIFT_LIST  = 1004;//礼包列表
// const WEB             = 1005;//需要传web_url
// const HOT_ACTIVITY    = 1006;//热门活动
// const SPEEDUP         = 1009;//加速版
// const DYNAMIC         = 1010;//动态
// const SUBSCRIBE       = 1011;//新游预约
// const EXCHANGE        = 1015;//限时兑换
// const MISSION         = 1016;//赚金任务
// const TRADE           = 1017;//账号交易
// const FUN_CARD        = 1018;//畅玩卡
// const MOD_GAME        = 1019;//MOD游戏
// const RECOMMEND       = 1020;//推荐
// const VIDEO           = 1021;//视频
// const JIANLOU         = 1022;//捡漏
// const CONPON          = 1023;//领券中心
// const PTB_RECHARGE    = 1024;//平台币充值
// const OPEN_SERVICE    = 1025;//开服列表
// const NEW_GAME        = 1026;//新游首发
// const RANKING         = 1027;//排行榜
// const CARD_HUB        = 1028;//礼包中心
// const GOLD_TURNTABLE  = 1029;//大转盘
// const ZERO_GAME       = 1030;//0元畅玩
// const ZHUANYOU_GAME   = 1031;//转游中心
// const GOLD_MALL       = 1032;//金币商城
// const GOLD_DUOBAO     = 1033;//金币夺宝
// const CARD648         = 1034;//614礼包
// const UP_GAME         = 1035;//UP游戏
// const JYZX            = 1036;//交易中心
// const FLZX            = 1037;//福利中心
// const BT_GAME         = 1038;//变态游戏
// const BIG_GAME        = 1039;//大厂游戏
// const BT_GAME_NEW     = 1040;//bt游戏分类
// const CWB_TAB_MOD     = 1041;//畅玩版分类   extra_id   type_id  cate_id
// const CWB_TAB_JSB     = 1042;//畅玩版加速版 金刚区
// const CWB_TAB_BTB     = 1043;//畅玩版变态版 金刚区
// const CWB_TAB_UP      = 1044;//畅玩版UP 金刚区
// const CWB_TAB_HOT_UP  = 1045;//畅玩版合集
// const CWB_TAB_MOD_NEW = 1046;//畅玩版分类-最新列表 金刚区
// const CWB_TAB_BT_RANK = 1047;//畅玩版变态排行榜
// const ACTION_RECYCLE  = 1048;//小号回收
// const GAME_LIST_CLASS = 1049;//需要传extra_id(游戏分类)
// const SAVINGS_CARD    = 1050;//需要传extra_id(游戏分类)

// const BT_GAME_CATE = 1040;//BT分类
