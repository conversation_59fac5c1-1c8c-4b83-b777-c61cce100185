export const TYPE_HOME = 0; //首页
export const TYPE_GAME = 1; //游戏详情
export const TYPE_GIFT = 2; //礼包详情
export const TYPE_NEWS = 3; //资讯
export const TYPE_WEB_VIEW = 4; //内部webview
export const TYPE_BROWSER = 5; //外部浏览器
export const TYPE_REPLY = 6; //回复
export const TYPE_SUBSCRIBE = 7; //我的预约
export const TYPE_REBATE = 8; //返利申请记录
export const TYPE_RECOMMEND = 100; // 系统通知 游戏推荐
export const TYPE_TRADE = 200; // 系统通知 小号交易审核
export const TYPE_TRADE_SUCCESS = 300; // 系统通知 小号交易成功
export const TYPE_GAME_CLOSE = 400; // 系统通知 游戏关服通知
export const TYPE_TRADE_BARGAIN = 401; //砍价通知
export const TYPE_TRADE_APPOINT = 402; //指定通知

export default {
  [TYPE_HOME]: 'QualitySelect',
  [TYPE_GAME]: 'GameDetail',
  // [TYPE_GIFT]: "GiftDetail",
  // [TYPE_NEWS]: "Iframe",
  [TYPE_WEB_VIEW]: 'Iframe',
  [TYPE_BROWSER]: 'Iframe',
  [TYPE_REPLY]: 'Notice',
  // [TYPE_SUBSCRIBE]: "预约",
  [TYPE_REBATE]: 'Rebate',
  [TYPE_RECOMMEND]: 'QualitySelect',
  [TYPE_TRADE]: 'MySellList',
  [TYPE_TRADE_SUCCESS]: 'MyBuyList',
  // [TYPE_GAME_CLOSE]: "游戏关服通知",
  [TYPE_TRADE_BARGAIN]: 'MyBargainList',
  [TYPE_TRADE_APPOINT]: 'MyBargainList',
};
