<template>
  <div class="user-recommend-item" @click="toRecommendDetail(info)">
    <div class="user-bar">
      <user-avatar class="avatar" :src="info.user.avatar" :self="false" />
      <div class="nickname" :class="{ orange: info.user.is_official == 1 }">
        {{ info.user.nickname }}
      </div>
      <div class="exp-level" v-if="info.user.exp_level_name">
        {{ info.user.exp_level_name }}
      </div>
      <div
        v-if="info.user.pay_level_name"
        class="pay-level"
        :style="{ backgroundColor: info.user.pay_level_color }"
      >
        {{ info.user.pay_level_name }}
      </div>
    </div>
    <div class="recommend-content">
      {{ info.content }}
    </div>
    <div class="recommend-game" @click.stop="">
      <game-item-2 :gameInfo="info.game"></game-item-2>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  methods: {
    toRecommendDetail(info) {
      this.toPage('UserRecommendDetail', { id: info.id });
    },
  },
};
</script>

<style lang="less" scoped>
.user-recommend-item {
  box-sizing: border-box;
  width: 339 * @rem;
  background: #ffffff;
  box-shadow: 0 * @rem 3 * @rem 11 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
  border-radius: 10 * @rem;
  overflow: hidden;
  margin: 0 auto;
  padding: 15 * @rem;
  .user-bar {
    display: flex;
    align-items: center;
    .avatar {
      width: 30 * @rem;
      height: 30 * @rem;
      background-color: #ccc;
      flex-shrink: 0;
    }
    .nickname {
      font-size: 17 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 15 * @rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.orange {
        color: #fe6600;
      }
    }
    .exp-level {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 17 * @rem;
      line-height: 17 * @rem;
      border-radius: 2 * @rem;
      background-color: #309af8;
      padding: 0 5 * @rem;
      font-size: 10 * @rem;
      font-weight: 500;
      color: #ffffff;
      margin-left: 8 * @rem;
      flex-shrink: 0;
    }
    .pay-level {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 17 * @rem;
      line-height: 17 * @rem;
      border-radius: 2 * @rem;
      background-color: #ed9239;
      padding: 0 5 * @rem;
      font-size: 10 * @rem;
      font-weight: 500;
      color: #ffffff;
      margin-left: 6 * @rem;
      flex-shrink: 0;
    }
    .top-level {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 17 * @rem;
      line-height: 17 * @rem;
      border-radius: 2 * @rem;
      background-color: #f34a4a;
      padding: 0 5 * @rem;
      margin-left: 6 * @rem;
      flex-shrink: 0;
      .top-icon {
        width: 8 * @rem;
        height: 8 * @rem;
        .image-bg('~@/assets/images/games/top-icon.png');
      }
      .top-text {
        font-size: 10 * @rem;
        color: #ffffff;
        font-weight: 500;
        margin-left: 2 * @rem;
      }
    }
  }
  .recommend-content {
    overflow: hidden;
    max-height: 71 * @rem;
    color: #797979;
    font-size: 13 * @rem;
    line-height: 18 * @rem;
    margin-top: 8 * @rem;
  }
  .recommend-game {
    box-sizing: border-box;
    padding: 0 10 * @rem;
    background: #f5f5f6;
    border-radius: 12px;
    margin-top: 8 * @rem;
    /deep/ .game-item-components .tags .tag {
      background-color: #ebebeb;
    }
  }
}
</style>
