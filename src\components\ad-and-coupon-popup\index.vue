<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    class="ad-and-coupon-popup"
    :overlay-style="{ 'z-index': '3000' }"
  >
    <img :src="data.bg_img_url" @click="toDetail()" />
    <div @click="popup = false" class="close"></div>
  </van-dialog>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { PageName } from '@/utils/actionCode.js';

export default {
  computed: {
    popup: {
      get() {
        return this.showAdPopup;
      },
      set(value) {
        this.setShowAdPopup(value);
      },
    },
    ...mapGetters({
      showAdPopup: 'user/showAdPopup',
      data: 'user/adData',
    }),
  },
  methods: {
    toDetail() {
      this.popup = false;
      // 1033 后台配置为活动地址 强行拦截 ------2022年9月30日17:49:31
      if (this.data.action_code == 1033) {
        this.toPage('Activity', { url: this.data.web_url });
        return false;
      }
      switch (PageName[this.data.action_code]) {
        case 'GameDetail':
          this.toPage('GameDetail', { id: this.data.extra_id });
          break;
        case 'Activity':
          this.toPage('Activity', { url: this.data.web_url });
          break;
      }
    },
    ...mapMutations({
      setShowAdPopup: 'user/setShowAdPopup',
    }),
  },
};
</script>

<style lang="less" scoped>
.ad-and-coupon-popup {
  z-index: 3000 !important;
  background: none;
  overflow: unset;
}
.close {
  position: absolute;
  right: 0;
  top: -26 * @rem;
  width: 26 * @rem;
  height: 26 * @rem;
  background-image: url(~@/assets/images/close.png);
  background-size: 100%;
  background-repeat: no-repeat;
}
</style>
