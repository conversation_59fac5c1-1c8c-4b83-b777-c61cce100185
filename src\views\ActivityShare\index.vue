<template>
  <div class="share-page">
    <div class="pic-cont" @click="download()">
      <img src="~@/assets/images/active/share/active_share_pic1.png" alt="" />
    </div>
    <div class="pic-cont" @click="download()">
      <img src="~@/assets/images/active/share/active_share_pic2.png" alt="" />
    </div>
    <div class="pic-cont" @click="download()">
      <img src="~@/assets/images/active/share/active_share_pic3.png" alt="" />
    </div>
    <div class="wxqq-popup" v-if="wxpopupShow" @click="hideWxpopup()">
      <img src="~@/assets/images/wxqq.png" alt="" />
    </div>
  </div>
</template>

<script>
import { ApiActivityDragonYearShare } from '@/api/views/activity';
import h5page from '@/utils/h5Page.js';
export default {
  name: 'Share',
  data() {
    return {
      isIOS: false,
      isWX: false,
      wxpopupShow: false,
      userid: '',
      channel: '',
      share_time: '',
      time_out: null,
      azurl: 'https://xz.c3733.cn/apk/gamebox/latest/3733gamebox_cps4851.apk',
      iosurl:
        'https://game.3733.com/#/activity/https:%2F%2Factivity.3733.com%2F%23%2Fspring_activity',
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const ua = navigator.userAgent.toLowerCase();
      this.isIOS = /iphone|ipad|ipod|ios|Macintosh/i.test(ua);
      this.isWX = /micromessenger\/[0-9]/i.test(ua) || /qq\/[0-9]/i.test(ua);
      const urlParams = new URLSearchParams(window.location.search);
      this.userid = urlParams.get('user_id');
      this.channel = urlParams.get('channel');
      this.share_time = urlParams.get('share_time');
      this.azurl = `https://xz.c3733.cn/apk/gamebox/latest/3733gamebox_${
        this.channel || 'cps4851'
      }.apk`;
      this.iosurl = `https://${h5page.env}game.3733.com/#/activity/https:%2F%2F${h5page.env}activity.3733.com%2F%23%2Fspring_activity`;
      await ApiActivityDragonYearShare({
        channel: this.channel,
        share_mem_id: this.userid,
        share_time: this.share_time,
      });
    },
    download() {
      if (this.isWX) {
        this.wxpopupShow = true;
        return;
      }
      if (this.isIOS) {
        window.location.href = this.iosurl;
      } else {
        // this.copy();
        this.toBox(this.azurl);
      }
    },
    copy() {
      this.$copyText('aaaaa').then(
        res => {},
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
          });
        },
      );
    },
    toBox(url) {
      clearTimeout(this.time_out);
      var visibilityChange;
      if (typeof document.hidden !== 'undefined') {
        // Opera 12.10 and Firefox 18 and later support
        visibilityChange = 'visibilitychange';
      } else if (typeof document.msHidden !== 'undefined') {
        visibilityChange = 'msvisibilitychange';
      } else if (typeof document.webkitHidden !== 'undefined') {
        visibilityChange = 'webkitvisibilitychange';
      }
      this.time_out = setTimeout(function () {
        window.location.href = url;
      }, 1500);
      document.addEventListener(visibilityChange, function () {
        clearTimeout(this.time_out);
      });
      window.location.href = `a3733://launch?ac=13&wu=https:%2F%2F${h5page.env}activity.3733.com%2F%23%2Fspring_activity`;
    },
    hideWxpopup() {
      this.wxpopupShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.share-page {
  background: linear-gradient(90deg, #1c2659 0%, #222f60 100%);
  img {
    width: 100%;
  }

  .pic-cont {
    width: 100%;
  }

  .wxqq-popup {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;

    img {
      width: 80%;
      height: auto;
      position: absolute;
      top: 0;
      right: 10 * @rem;
    }
  }
}
</style>
