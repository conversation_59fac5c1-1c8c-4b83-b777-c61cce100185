<template>
  <div class="page check-pay-result">
    <div class="result-icon">
      <img src="@/assets/images/check-pay-success.png" v-if="status == 1" />
      <img src="@/assets/images/check-pay-fail.png" v-if="status == 0" />
    </div>
    <div class="result-text">{{ statusText }}</div>
    <div class="fixed-bottom btn" @click="goBack"> 返回商家 </div>
  </div>
</template>

<script>
import { getQueryVariable } from '@/utils/function.js';
import { ApiGetOrderStatus } from '@/api/views/recharge.js';
import { isWebApp } from '@/utils/userAgent.js';
export default {
  data() {
    return {
      order_id: '', // TVIP1717741156012730001
      order_type: '',

      status: null,
    };
  },
  computed: {
    statusText() {
      return this.status == 1
        ? '支付成功'
        : this.status == 0
        ? '支付失败'
        : '查询中';
    },
  },
  async created() {
    this.order_id = getQueryVariable('order_id');
    this.order_type = getQueryVariable('order_type');
    await this.checkResult();
  },
  methods: {
    async checkResult() {
      try {
        const res = await ApiGetOrderStatus({
          order_id: this.order_id,
          order_type: this.order_type,
        });
        if (res.code == 1) {
          // 交易成功
          this.status = 1;
        }
      } catch (e) {
        // 交易失败
        if (e.code != 1) {
          this.status = 0;
        }
      }
    },
    goBack() {
      if (isWebApp) {
        this.back();
      } else {
        this.$toast('支付已完成，请手动返回');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.check-pay-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  .result-icon {
    width: 94 * @rem;
    height: 94 * @rem;
    margin-top: 130 * @rem;
  }
  .result-text {
    margin-top: 15 * @rem;
    font-size: 21 * @rem;
    font-weight: bold;
    line-height: 31 * @rem;
  }
  .fixed-bottom {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 113 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 225 * @rem;
    height: 50 * @rem;
    border-radius: 31 * @rem;
    border: 1 * @rem solid #dddddd;
    font-size: 16 * @rem;
    color: #666666;
  }
}
</style>
