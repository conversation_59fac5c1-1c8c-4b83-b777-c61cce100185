import { request } from '../index';
import h5Page from '@/utils/h5Page';
import { pay } from '@/utils/function';

// 平台币充值创建订单
/**
 * @param isNew 1 固定为1
 * @param money 充值金额
 * @param payWay 支付方式  微信：wx  支付宝：zfb
 *  */
export function ApiCreateOrderPtb(params = {}) {
  return request('/web/platform/createOrderPtb', params);
}

// 平台币信息
export function ApiPlatformGetInfo(params = {}) {
  return request('/web/platform/getInfo', params);
}

// 平台币明细
/**
 * @param page 1
 *  */
export function ApiPlatCoinDetail(params = {}) {
  return request(
    '/float/box/ajax_to_platcoin_detail',
    params,
    false,
    h5Page.api_url2,
  );
}

// 金币明细
/**
 * @param page 1
 *  */
export function ApiGoldDetail(params = {}) {
  return request(
    '/float/box/ajax_to_gold_detail',
    params,
    false,
    h5Page.api_url2,
  );
}

// 获取支付链接
/**
 * @param orderType "类型 游戏内消费：101  充值平台币 ：102  购买SVIP：103  小号交易：102  小号捡漏：202  购买畅玩卡：104  超值回馈3元礼包：105",
 * @param packageName 包名   放空
 * @param payWay 支付方式  微信：wx  支付宝：zfb zfb_dmf 平台币:ptb 金币:gold
 * @param orderId 订单id
 *  */
export function ApiGetPayUrl(params = {}) {
  return pay('/pay/api/getPayUrl', params, false);
  // return request('/pay/api/getPayUrl', params, false);
}

// svip 页面数据
export function ApiSvipIndex(params = {}) {
  return request('/web/svip/index', params, false);
}

// SVIP下单，并获取支付链接
/**
 * @param month 充值月数
 * @param amount 金额
 * @param rebate_ptb 立返的平台币
 * @param payWay 支付方式  微信：wx  支付宝：zfb
 * @param is_cycle 0
 *  */
export function ApiCreateOrderSvip(params = {}) {
  return request('/web/svip/createOrderSvip', params);
}

/* svip  头像、昵称、SVIP到期时间    
svip_status:svip状态，1=是，2=过期，3=从来不曾购买过 
is_cycle_user:是否为周期付费用户 1=是，0=否 */
/**
 *  */
export function ApiGetUserSvipInfo(params = {}) {
  return request('/web/svip/getUserSvipInfo', params);
}

/* 畅玩卡页面数据 */
/**
 *  */
export function ApiGameCardIndex(params = {}) {
  return request('/web/game_card/index', params);
}

/* 畅玩卡页面-用户信息 */
/**
 *  */
export function ApiGetUserCard(params = {}) {
  return request('/web/game_card/getUserCard', params);
}

/* 畅玩卡下单 */
/**
 * @param type 类型 周卡:1  月卡：2  季卡：3  年卡：4
 * @param payWay 支付方式 微信：wx 支付宝：zfb
 *  */
export function ApiGameCardCreateOrder(params = {}) {
  return request('/web/game_card/createOrder', params);
}

/* 实时查询游戏是否可以使用畅玩卡代金券 */
/**
 * @param keyword 关键词
 *  */
export function ApiGameCardSearchGame(params = {}) {
  return request('/web/game_card/searchGame', params);
}

/* 6倍返利 详情 */
export function ApiRequiteIsCharge(params = {}) {
  return request('/web/requite/isCharge', params);
}

/* 6倍返利创建订单 */
/**
 * @param type 1：3元礼包  2：加量包
 * @param payWay wx 、 zfb
 * @param money 100
 * @param isNew 固定为1
 *  */
export function ApiRequiteCreateOrder(params = {}) {
  return request('/web/requite/createOrderAjax', params);
}

/* 获取订单信息，用于云游戏内订单 */
/**
 * @param orderId 订单ID
 * @param orderType
 * @param money 100
 * @param isNew 固定为1
 *  */
export function ApiGetOrderInfo(params = {}) {
  return request('/pay/api/getOrderInfo', params);
}

/* 获取订单记录 */
/**
 * @param status 1=未支付  2=已支付
 *  */
export function ApiGetPayRecordList(params = {}) {
  return request('/pay/api/payRecordList', params, false);
}

/* 取消订单 */
/**
 * @param orderId 订单ID
 *  */
export function ApiCancelOrder(params = {}) {
  return request('/api/order/close', params);
}

/* 获取订单状态 */
/**
 * @param {string} order_id 订单号
 * @param {string} order_type 游戏内消费： 101  充值平台币 ：102  购买SVIP：103  小号交易：102  小号捡漏：202  购买畅玩卡：104  超值回馈3元礼包：105
 *  */
export function ApiGetOrderStatus(params = {}) {
  return request('/web/game_pay/getOrderStatus', params);
}

/* 金币兑换代金券接口 */
/**
 * @param {number} money 兑换的钱
 *  */
export function ApiExchangeCoupon(params = {}) {
  return request('/web/goldcoin/exchangeCoupon', params);
}

/* 省钱卡 - 列表 */
export function ApiSavingsCardIndex(params = {}) {
  return request('/api/savings_card/index', params);
}
/* 省钱卡 - 下单 */
export function ApiSavingsCardCreateOrder(params = {}) {
  return request('/api/savings_card/createOrder', params);
}
/* 省钱卡 - 领取金币 */
export function ApiSavingsCardTakeReceive(params = {}) {
  return request('/api/savings_card/takeReceive', params);
}
