import { request } from '../index';

/**
 * 搜索
 * @param keyword
 * @param fromAction 1-主动输入关键词搜索，2-点击热门搜索，3-点击历史搜索，4-刷新、重试，5-加载更多，6-切换tab触发
 * @param type 1-全部游戏(默认)，101-礼包，201-资讯, 301-视频 php说传401
 * @param orderType 1按推荐数倒排，2按排序时间倒排
 */
export function ApiSearchIndex(params = {}) {
  return request('/api/search/index', params);
}

/**
 * 搜索 4.4 热门搜索分类
 */
export function ApiSearchGetHotKey(params = {}) {
  return request('/api/search/getHotKey', params);
}
