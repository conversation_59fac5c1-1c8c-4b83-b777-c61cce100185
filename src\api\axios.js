import Vue from 'vue';
import axios from 'axios';
import Router from '@/router';

// 创建 axios 实例
let service = axios.create({
  // timeout: 20000,  // 请求超时时间
  crossDomain: true, //设置cross跨域
  withCredentials: false, //设置cross跨域 并设置访问权限 不允许跨域携带cookie信息
});

// 拦截请求
service.interceptors.request.use(req => {
  return req;
});

// 拦截响应
service.interceptors.response.use(
  res => {
    return res;
  },
  err => {
    if (process.env.NODE_ENV == 'development') {
      console.log(err);
    }
    return Promise.reject(err);
  },
);

export { service };
