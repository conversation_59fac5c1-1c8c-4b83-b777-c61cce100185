import { isIosBox } from './userAgent';

export let globalOpenWindow,
  globalOpenNavWindow,
  globalOpenDownload,
  globalOpenSettingFile,
  globalOpenOtherGrq,
  boxGetData;

if (isIosBox) {
  // 打开新页面
  globalOpenWindow = url => {
    window.webkit.messageHandlers.openInNewWindow.postMessage(url);
  };
  // 打开带头部的新页面
  globalOpenNavWindow = (url, title) => {
    window.webkit.messageHandlers.openInNewNavWindow.postMessage(url, title);
  };
  // 套壳环境下下载游戏
  globalOpenDownload = url => {
    window.webkit.messageHandlers.openInBrowser.postMessage(url);
  };
  // 套壳环境下下载配置文件,双url才能跳转到配置文件
  globalOpenSettingFile = res => {
    window.webkit.messageHandlers.openInBrowser.postMessage(
      `${res.jump_url}?url1=${res.mobileconfig_url}&url2=${res.mobileprovision_url}`,
    );
  };
  // 套壳环境下第三方个人签，纸片内测
  globalOpenOtherGrq = url => {
    window.webkit.messageHandlers.openInBrowser.postMessage(url);
  };
  /**
   * 获取盒子一些数据
   *  scheme 包名
   *  */
  boxGetData = () => {
    if (window.getData) {
      return JSON.parse(window.getData);
    } else {
      return undefined;
    }
  };
} else {
  globalOpenWindow = url => {
    // window.open(url);
    window.location.href = url;
  };
  globalOpenNavWindow = url => {
    // window.open(url);
    window.location.href = url;
  };
  globalOpenDownload = url => {
    window.location.href = url;
  };
  globalOpenSettingFile = res => {
    window.location.href = res.mobileconfig_url;
    setTimeout(() => {
      // 非套壳情况需要第二遍让其自动跳转
      window.location.href = res.mobileprovision_url;
    }, 1000);
  };
  globalOpenOtherGrq = url => {
    // window.open(url);
    window.location.href = url;
  };
  boxGetData = undefined;
}
