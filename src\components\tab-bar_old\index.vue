<template>
  <div
    class="tab-bar-component"
    v-show="platform != 'android' && platform != 'ios'"
  >
    <div class="tab-bar-fixed">
      <div class="tab-list">
        <div
          class="tab-item btn"
          v-for="(item, index) in tabBarList"
          :key="index"
          @click="switchTab(item.name, item.params, index)"
        >
          <!-- 根据dl_config隐藏福利中心的按钮 -->
          <template v-if="!(index == 2 && dlConfig == 1)">
            <div class="fixed-tag" v-if="showTag(index)">
              {{ tagContent(index) }}
            </div>
            <div
              class="tab-icon"
              :class="[{ active: $route.path.includes(item.route) }, item.desc]"
            >
              <div class="dot" v-if="index == 4 && hasMineDot"></div>
            </div>
            <span
              class="tab-text"
              :class="[{ active: $route.path.includes(item.route) }]"
              >{{ item.text }}</span
            >
          </template>
        </div>
      </div>
      <bottom-safe-area bgColor="#fff"></bottom-safe-area>
    </div>
  </div>
</template>

<script>
import { platform } from '@/utils/box.uni.js';
import { themeColorLess } from '@/common/styles/_variable.less';
import { mapGetters, mapActions } from 'vuex';
import { getDayZeroTimestamp } from '@/utils/function.js';
export default {
  name: 'tabBar',
  data() {
    return {
      platform,
      themeColorLess,
      tabBarList: [
        {
          text: this.$t('首页'),
          name: 'QualitySelect',
          route: 'home',
          desc: 'home-icon',
          params: {},
        },
        {
          text: this.$t('分类'),
          name: 'Category',
          route: 'category',
          desc: 'category-icon',
          params: {},
        },
        {
          text: '',
          name: 'Welfare',
          route: 'welfare',
          desc: 'welfare-icon',
          params: {},
        },
        {
          text: this.$t('交易'),
          name: 'RoleTransfer',
          route: 'role_transfer',
          desc: 'deal-icon',
          params: {},
        },
        {
          text: this.$t('我的'),
          name: 'Mine',
          route: 'mine',
          desc: 'mine-icon',
          params: {},
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      dotInfo: 'system/dotInfo',
      dlConfig: 'system/dlConfig',
      tabBarAngle: 'system/tabBarAngle',
      clickedTabBarAngle: 'system/clickedTabBarAngle',
    }),
    hasMineDot() {
      let { feedback_read, reply_read, inform_read } = this.dotInfo;
      return !feedback_read && !reply_read && !inform_read ? false : true;
    },
  },
  methods: {
    ...mapActions({
      SET_CLICKED_TAB_BAR_ANGLE: 'system/SET_CLICKED_TAB_BAR_ANGLE',
    }),
    showTag(index) {
      let findResult = this.tabBarAngle.findIndex(item => {
        return item.navigation == index + 1;
      });
      let clicked = this.clickedTabBarAngle.find(item => {
        return item.navigation == index + 1;
      });
      if (findResult != -1) {
        if (
          clicked &&
          clicked.dateTimeStamp ==
            getDayZeroTimestamp(new Date().getTime() / 1000)
        ) {
          return false;
        }
        return true;
      } else {
        return false;
      }
    },
    tagContent(index) {
      let findResult = this.tabBarAngle.find(item => {
        return item.navigation == index + 1;
      });
      if (findResult) {
        return findResult.angle;
      } else {
        return false;
      }
    },
    switchTab(name, params = {}, index) {
      // 根据dl_config隐藏福利中心的按钮
      if (index == 2 && this.dlConfig == 1) {
        return false;
      }
      // 点击tabbar消除角标
      this.SET_CLICKED_TAB_BAR_ANGLE(index + 1);
      //跳转页面
      this.$router.push({ name: name, params: params });
    },
  },
};
</script>

<style lang="less" scoped>
.tab-bar-component {
  width: 100%;
  // height: 77 * @rem;
  // padding-bottom: @safeAreaBottom;
  // padding-bottom: @safeAreaBottomEnv;
  // flex-shrink: 0;
  height: 0;
}
.tab-bar-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  .fixed-center;
  z-index: 2000;
  background: url(~@/assets/images/tab-bar/tab-bar-bg.png) center top no-repeat;
  background-size: 100% 77 * @rem;
  .tab-list {
    height: 50 * @rem;
    display: flex;
    margin-top: 27 * @rem;
    .tab-item {
      display: flex;
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .fixed-tag {
        position: absolute;
        left: 50%;
        top: -14 * @rem;
        height: 18 * @rem;
        display: flex;
        align-items: center;
        background: linear-gradient(270deg, #ff4ca2 0%, #ff5353 100%);
        border-radius: 9 * @rem 9 * @rem 9 * @rem 3 * @rem;
        z-index: 1000;
        color: #ffffff;
        font-size: 10 * @rem;
        padding: 0 6 * @rem;
        white-space: nowrap;
        box-shadow: 0 0 6 * @rem rgba(0, 0, 0, 0.2);
        animation: move 0.3s ease infinite;
        transform-origin: 10 * @rem center;
      }
      .tab-icon {
        width: 24 * @rem;
        height: 24 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        position: relative;
        .dot {
          position: absolute;
          right: -5 * @rem;
          top: 0 * @rem;
          width: 5 * @rem;
          height: 5 * @rem;
          border-radius: 50%;
          background-color: #fe4a55;
        }
        &.home-icon {
          background-image: url(../../assets/images/tab-bar/home.png);
          background-size: 24 * @rem 24 * @rem;
          &.active {
            background-image: url(../../assets/images/tab-bar/home-selected.png);
          }
        }
        &.category-icon {
          background-image: url(../../assets/images/tab-bar/category.png);
          background-size: 24 * @rem 24 * @rem;
          &.active {
            background-image: url(../../assets/images/tab-bar/category-selected.png);
          }
        }
        &.welfare-icon {
          width: 66 * @rem;
          height: 68 * @rem;
          background-image: url(../../assets/images/tab-bar/welfare.png);
          background-size: 66 * @rem 68 * @rem;
          margin-top: -12 * @rem;
          &.active {
            background-image: url(../../assets/images/tab-bar/welfare.png);
          }
        }
        &.deal-icon {
          background-image: url(../../assets/images/tab-bar/deal.png);
          background-size: 24 * @rem 24 * @rem;
          &.active {
            background-image: url(../../assets/images/tab-bar/deal-selected.png);
          }
        }
        &.mine-icon {
          background-image: url(../../assets/images/tab-bar/mine.png);
          background-size: 24 * @rem 24 * @rem;
          &.active {
            background-image: url(../../assets/images/tab-bar/mine-selected.png);
          }
        }
      }
      .tab-text {
        margin-top: 3 * @rem;
        color: #a4a4a4;
        font-size: 9 * @rem;
        line-height: 13 * @rem;
        &.active {
          color: #fe6600;
        }
      }
    }
  }
}
@keyframes move {
  0%,
  100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}
</style>
