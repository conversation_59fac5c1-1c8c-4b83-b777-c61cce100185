export default {
  state: {
    showEwmPopup: false, //二维码弹窗显示
    ewmSrc: '', // 需要生成二维码的地址
  },
  mutations: {
    setShowEwmPopup(state, showEwmPopup) {
      state.showEwmPopup = showEwmPopup ?? false;
    },
    setEwmSrc(state, ewmSrc) {
      state.ewmSrc = ewmSrc ? ewmSrc : '';
    },
  },
  getters: {
    showEwmPopup(state) {
      return state.showEwmPopup;
    },
    ewmSrc(state) {
      return state.ewmSrc;
    },
  },
};
