<template>
  <div
    class="tab-bar-component"
    v-show="platform != 'android' && platform != 'ios'"
  >
    <div class="tab-bar-fixed">
      <div class="tab-list">
        <template v-for="(item, index) in tabBarList">
          <div
            class="tab-item btn"
            :key="index"
            @click="switchTab(item.name, item.params, index)"
            v-if="
              !(item.name == 'Welfare' && hideJfq == 1) &&
              !(item.name == 'MyGame' && (dlConfig == 1 || showMyGame == 0))
            "
          >
            <div
              class="tab-icon"
              :class="[{ active: $route.path.includes(item.route) }, item.desc]"
            >
              <div
                class="dot"
                v-if="item.name == 'Mine' && unreadCount.sum > 0"
              >
                {{ unreadCount.sum > 99 ? '99+' : unreadCount.sum }}
              </div>
            </div>
            <span
              class="tab-text"
              :class="[{ active: $route.path.includes(item.route) }]"
              >{{ item.text }}</span
            >
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { platform } from '@/utils/box.uni.js';
import { themeColorLess } from '@/common/styles/_variable.less';
import { mapGetters, mapActions } from 'vuex';
import { getDayZeroTimestamp } from '@/utils/function.js';
export default {
  name: 'tabBar',
  data() {
    return {
      platform,
      themeColorLess,
    };
  },
  computed: {
    ...mapGetters({
      unreadCount: 'system/unreadCount',
      hideJfq: 'system/hideJfq',
      tabBarAngle: 'system/tabBarAngle',
      clickedTabBarAngle: 'system/clickedTabBarAngle',
      dlConfig: 'system/dlConfig',
      initData: 'system/initData',
      isUpApp: 'system/isUpApp',
      showMyGame: 'system/showMyGame',
    }),
    tabBarList() {
      let formaTabBar = [
        {
          text: this.$t('首页'),
          name: 'QualitySelect',
          route: 'home',
          desc: 'home-icon',
          params: {},
        },
        {
          text: this.$t('分类'),
          name: 'Category',
          route: 'category',
          desc: 'category-icon',
          params: {},
        },
        {
          text: this.$t('我的游戏'),
          name: 'MyGame',
          route: 'my_game',
          desc: 'my-game-icon',
          params: {},
        },
        {
          text: this.$t('福利中心'),
          name: 'Welfare',
          route: 'welfare',
          desc: 'welfare-icon',
          params: {},
        },
        {
          text: this.$t('我的'),
          name: 'Mine',
          route: 'mine',
          desc: 'mine-icon',
          params: {},
        },
      ];
      if (this.isUpApp) {
        formaTabBar.splice(
          1,
          2,
          {
            text: '游戏库',
            name: 'GameLibrary',
            route: 'game_library',
            desc: 'my-game-icon',
            params: {},
          },
          {
            text: '排行',
            name: 'UpRank',
            route: 'up_rank',
            desc: 'rank-icon',
            params: {},
          },
        );
      }
      return formaTabBar;
    },
  },
  methods: {
    ...mapActions({
      SET_CLICKED_TAB_BAR_ANGLE: 'system/SET_CLICKED_TAB_BAR_ANGLE',
    }),
    showTag(index) {
      let findResult = this.tabBarAngle.findIndex(item => {
        return item.navigation == index + 1;
      });
      let clicked = this.clickedTabBarAngle.find(item => {
        return item.navigation == index + 1;
      });
      if (findResult != -1) {
        if (
          clicked &&
          clicked.dateTimeStamp ==
            getDayZeroTimestamp(new Date().getTime() / 1000)
        ) {
          return false;
        }
        return true;
      } else {
        return false;
      }
    },
    tagContent(index) {
      let findResult = this.tabBarAngle.find(item => {
        return item.navigation == index + 1;
      });
      if (findResult) {
        return findResult.angle;
      } else {
        return false;
      }
    },
    switchTab(name, params = {}, index) {
      // 根据dl_config隐藏福利中心的按钮
      if (name == 'Welfare' && this.hideJfq == 1) {
        return false;
      }
      // 点击tabbar消除角标
      this.SET_CLICKED_TAB_BAR_ANGLE(index + 1);
      //跳转页面
      this.$router.push({ name: name, params: params });
    },
  },
};
</script>

<style lang="less" scoped>
.tab-bar-component {
  width: 100%;
  // height: 50 * @rem;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  flex-shrink: 0;
}
.tab-bar-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50 * @rem;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  .fixed-center;
  border-top: 1px solid #f5f5f6;
  background: #fff;
  z-index: 2000;
  .tab-list {
    height: 50 * @rem;
    display: flex;
    .tab-item {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .tab-icon {
        width: 22 * @rem;
        height: 22 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 24 * @rem 24 * @rem;
        position: relative;
        .dot {
          position: absolute;
          left: 70%;
          top: -5 * @rem;
          padding: 0 5 * @rem;
          height: 14 * @rem;
          border-radius: 7 * @rem;
          background-color: #fe4a55;
          color: #fff;
          font-size: 10 * @rem;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        &.home-icon {
          background-image: url(../../assets/images/tab-bar/home.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/home-selected.png);
          }
        }
        &.category-icon {
          background-image: url(../../assets/images/tab-bar/category.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/category-selected.png);
          }
        }
        &.my-game-icon {
          background-image: url(../../assets/images/tab-bar/my-game-icon.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/my-game-icon-selected.png);
          }
        }
        &.welfare-icon {
          background-image: url(../../assets/images/tab-bar/welfare-icon.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/welfare-icon-selected.png);
          }
        }
        &.mine-icon {
          background-image: url(../../assets/images/tab-bar/mine.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/mine-selected.png);
          }
        }
        &.rank-icon {
          background-image: url(../../assets/images/tab-bar/rank.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/rank-selected.png);
          }
        }
      }
      .tab-text {
        margin-top: 5 * @rem;
        color: #a4a4a4;
        font-size: 11 * @rem;
        &.active {
          color: @themeColor;
        }
      }
    }
  }
}
</style>
