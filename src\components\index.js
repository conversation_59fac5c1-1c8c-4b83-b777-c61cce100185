import Vue from 'vue';
import StatusBar from './status-bar';
import BottomSafeArea from './bottom-safe-area';
import DealItem from './deal-item';
import NavBar from './nav-bar';
import NavBar2 from './nav-bar-2';
import PullRefresh from './pull-refresh';
import ContentEmpty from './content-empty';
import LoadMore from './load-more';
import YyList from './yy-list';
import UserAvatar from './user-avatar';
import GameItem2 from './game-item-2';
import GameItem3 from './game-item-3';
import YyDownloadBtn from './yy-download-btn';
import YyDownloadPopup from './yy-download-popup';
import GameItemNew from './game-item-new';
import RecommendItem from './recommend-item';
import YyBanner from './yy-banner';
import RubberBand from './rubber-band';
import ChangeHref from './change-href';
import GoldGameDialog from './gold-game-dialog';
import CouponGameDialog from './coupon-game-dialog';
import EwmPopup from './ewm-popup';
import CardpassCopyPopup from './cardpass-copy-popup';
//引入拖拽排序插件
import VueDND from 'awe-dnd';

Vue.use(VueDND);
// vant
import {
  Dialog,
  popover,
  Empty,
  Toast,
  List,
  Collapse,
  CollapseItem,
  DatetimePicker,
  Tab,
  Tabs,
  Sidebar,
  SidebarItem,
  DropdownMenu,
  DropdownItem,
  PullRefresh as PullRefresh2,
  Uploader,
  ImagePreview,
  RadioGroup,
  Radio,
  Swipe,
  SwipeItem,
  Popup,
  SwipeCell,
  Sticky,
  Lazyload,
  Loading,
  Rate,
  ActionSheet,
  IndexBar,
  IndexAnchor,
  Stepper,
  NoticeBar,
  CheckboxGroup,
  Checkbox,
  Switch,
  Icon,
  Field,
  Calendar,
} from 'vant';
Vue.use(Dialog)
  .use(popover)
  .use(Empty)
  .use(Toast)
  .use(List)
  .use(Collapse)
  .use(CollapseItem)
  .use(DatetimePicker)
  .use(Tab)
  .use(Tabs)
  .use(Sidebar)
  .use(SidebarItem)
  .use(DropdownMenu)
  .use(DropdownItem)
  .use(PullRefresh2)
  .use(Uploader)
  .use(ImagePreview)
  .use(Radio)
  .use(RadioGroup)
  .use(Swipe)
  .use(SwipeItem)
  .use(SwipeCell)
  .use(Popup)
  .use(Sticky)
  .use(Loading)
  .use(Rate)
  .use(ActionSheet)
  .use(IndexBar)
  .use(IndexAnchor)
  .use(Stepper)
  .use(NoticeBar)
  .use(CheckboxGroup)
  .use(Checkbox)
  .use(Switch)
  .use(Lazyload)
  .use(Field)
  .use(Icon)
  .use(Calendar);

// 复制插件
import VueClipboard from 'vue-clipboard2';
Vue.use(VueClipboard);

// 瀑布流
import waterfall from 'vue-waterfall2';
Vue.use(waterfall);
// 全局轮播
import VueAwesomeSwiper from 'vue-awesome-swiper';
import 'swiper/css/swiper.css';
Vue.use(VueAwesomeSwiper /* { default options with global component } */);

// 自定义组件对象
const components = {
  StatusBar,
  BottomSafeArea,
  DealItem,
  NavBar,
  NavBar2,
  PullRefresh,
  ContentEmpty,
  LoadMore,
  YyList,
  UserAvatar,
  GameItem2,
  GameItem3,
  YyDownloadBtn,
  YyDownloadPopup,
  GameItemNew,
  RecommendItem,
  YyBanner,
  RubberBand,
  ChangeHref,
  GoldGameDialog,
  CouponGameDialog,
  EwmPopup,
  CardpassCopyPopup,
};
// 自定义组件全局注册 ---s
Object.keys(components).forEach(key => {
  Vue.component(`${key}`, components[key]);
});
