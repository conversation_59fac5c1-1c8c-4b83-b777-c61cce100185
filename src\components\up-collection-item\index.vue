<template>
  <div class="up-collection-item" @click="toUpCollectionDetail">
    <div class="collection-title">{{ info.title }}</div>
    <div class="collection-subtitle">{{ info.subtitle }}</div>
    <div class="up-info">
      <div class="avatar">
        <user-avatar :src="info.up_info.avatar"></user-avatar>
      </div>
      <div class="nickname">{{ info.up_info.nickname }}</div>
    </div>
    <div class="up-banner" v-if="info.list_banner">
      <img :src="info.list_banner" alt="" />
    </div>
    <div class="game-list" v-else>
      <up-game-item
        class="game-item"
        v-for="game in info.game_list"
        :key="game.id"
        :info="game"
      ></up-game-item>
    </div>
  </div>
</template>

<script>
import upGameItem from '@/components/up-game-item';
export default {
  components: {
    upGameItem,
  },
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  methods: {
    toUpCollectionDetail() {
      this.toPage('UpCollectionDetail', { id: this.info.id });
    },
  },
};
</script>

<style lang="less" scoped>
.up-collection-item {
  box-sizing: border-box;
  width: 339 * @rem;
  background: linear-gradient(180deg, #eff3ff 0%, #ffffff 24%, #ffffff 100%);
  box-shadow: 0 0 8 * @rem 0 rgba(5, 0, 255, 0.08);
  border-radius: 10 * @rem;
  margin: 0 auto;
  padding: 20 * @rem 12 * @rem 15 * @rem;
  &:not(:first-of-type) {
    margin-top: 15 * @rem;
  }
  .collection-title {
    font-size: 18 * @rem;
    color: #333333;
    line-height: 23 * @rem;
    font-weight: bold;
  }
  .collection-subtitle {
    font-size: 13 * @rem;
    color: #9a9a9a;
    line-height: 16 * @rem;
    margin-top: 5 * @rem;
  }
  .up-info {
    display: flex;
    align-items: center;
    margin-top: 20 * @rem;
    .avatar {
      width: 24 * @rem;
      height: 24 * @rem;
    }
    .nickname {
      font-size: 14 * @rem;
      color: #333333;
      line-height: 16 * @rem;
      margin-left: 8 * @rem;
      flex: 1;
      min-width: 0;
    }
  }
  .game-list {
    background-color: #f6f9fc;
    border-radius: 10 * @rem;
    padding: 2 * @rem 0;
    margin-top: 15 * @rem;
  }
  .up-banner {
    width: 313 * @rem;
    height: 176 * @rem;
    border-radius: 10 * @rem;
    overflow: hidden;
    margin: 15 * @rem auto 0;
  }
}
</style>
