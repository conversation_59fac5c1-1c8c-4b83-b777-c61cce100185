<template>
  <!-- 复制礼包弹窗 -->
  <van-dialog
    v-model="popupShow"
    :close-on-click-overlay="true"
    message-align="left"
    :lock-scroll="false"
    class="copy-dialog"
    :show-confirm-button="false"
  >
    <template v-if="info.cardpass">
      <div class="title">
        <div class="title-icon"></div>
        <div class="title-text">礼包码</div>
      </div>
      <div class="cardpass">{{ info.cardpass }}</div>
      <div class="desc">{{ introduction }}</div>
      <div class="copy-btn btn" @click="copy(info.cardpass)"> 复制礼包码 </div>
    </template>
  </van-dialog>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
    introduction() {
      if (this.info.cardtext) {
        return `使用说明：${this.info.cardtext}`;
      } else {
        return `使用说明：请在游戏中使用`;
      }
    },
  },
  methods: {
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-button {
  border-radius: 16px;
}
.copy-dialog {
  box-sizing: border-box;
  width: 244 * @rem;
  border-radius: 12 * @rem;
  background-color: #fff;
  padding: 20 * @rem 16 * @rem 22 * @rem;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    .title-icon {
      width: 18 * @rem;
      height: 18 * @rem;
      .image-bg('~@/assets/images/games/gift-title-icon.png');
    }
    .title-text {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 4 * @rem;
    }
  }
  .cardpass {
    box-sizing: border-box;
    width: 209 * @rem;
    height: 39 * @rem;
    background-color: #f4f4f4;
    border-radius: 6 * @rem;
    display: flex;
    align-items: center;
    padding: 0 10 * @rem;
    margin-top: 13 * @rem;
    font-size: 13 * @rem;
    color: #000000;
    font-weight: 400;
    word-break: break-all;
  }
  .desc {
    font-size: 12 * @rem;
    line-height: 17 * @rem;
    color: #757575;
    font-weight: 400;
    margin-top: 13 * @rem;
    padding: 0 5 * @rem;
  }
  .copy-btn {
    width: 186 * @rem;
    height: 36 * @rem;
    margin: 20 * @rem auto 0;
    background: linear-gradient(96deg, #ff9f00 0%, #fe6600 100%);
    border-radius: 18 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
