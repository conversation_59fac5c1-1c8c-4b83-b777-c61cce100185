import { Toast, Dialog } from 'vant';
import store from '@/store';
import { request } from '@/api';
import router from '@/router';
import {
  ApiUserInfoEx,
  ApiNewUser,
  ApiUserSubmitInstallInfo,
  ApiUserSubmitLoginInfo,
  ApiUserSubmitRegisterInfo,
  ApiUserSubmitUserInfo,
} from '@/api/views/users';
import { ApiGameCheckDown, ApiGameDownloadAdded } from '@/api/views/game.js';
import {
  BOX_goToGame,
  BOX_openInBrowser,
  BOX_openInNewWindow,
  BOX_showActivity,
  BOX_getPackageName,
  BOX_openInNewNavWindow,
  BOX_openInBrowser_H5Download,
} from '@/utils/box.uni.js';
import BASEPARAMS from '@/utils/baseParams';
import i18n from '@/i18n';
import { authInfo, platform } from './box.uni';
import {
  isIos,
  isIosBox,
  isWeb<PERSON><PERSON>,
  is<PERSON><PERSON><PERSON>,
  needGuide,
  isAndroidBox,
} from '@/utils/userAgent';
import h5Page from '@/utils/h5Page';
import {
  ApiPersonalSign,
  ApiCheckUdid,
  ApiUserCheckGrqAuthStatus,
} from '@/api/views/users.js';

export function loginSuccess(res) {
  Toast.success(i18n.t('登录成功'));
  store.commit('user/setUserInfo', res.data);
  store.commit('gift/setXiaohaoMap');

  // 如果渠道号是804判断是不是第一次登录，是的话上报，不是的话不处理
  if (BASEPARAMS.channel === 'cps804') {
    if (!localStorage.getItem('804first')) {
      ApiNewUser({ packageName: getQueryVariable('packageName') });
      localStorage.setItem('804first', 1);
    }
  }

  // TrackingIO统计(上报服务端)
  if (
    !!store.getters['system/accessTrackingIO'] &&
    ['iosBox', 'androidBox'].includes(platform)
  ) {
    // 特定渠道才要上报 从store里拿

    let plat = 'ios';
    if (platform == 'androidBox') {
      plat = 'android';
    } else if (platform == 'iosBox') {
      plat = 'ios';
    }
    if (!res.data.new_user) {
      // 登录上报
      ApiUserSubmitLoginInfo({ plat });
    } else {
      // 注册上报
      ApiUserSubmitRegisterInfo({ plat });
    }

    // 登录之后上报玩家信息
    ApiUserSubmitUserInfo({ plat });
  }

  ApiUserInfoEx().then(async res2 => {
    store.commit('user/setUserInfoEx', res2.data);
    await store.dispatch('user/SET_USER_INFO');
    router.go(-1);
    await store.dispatch('user/SET_REGRESSION_POPUP'); // 回归用户处理
    await store.dispatch('user/SET_FIRST_COUPON_ICON'); // 首充条件
    if (!res2.data || parseInt(res2.data.auth_status) != 2) {
      setTimeout(() => {
        store.commit('user/setShowLCPopup', true);
      }, 0);
    }
    localStorage.setItem('STORE', JSON.stringify(store.state));
  });
}
// 下载按钮防沉迷check
export const checkDownload = async () => {
  const res = await ApiGameCheckDown();
  if (res.code > 0) {
    return true;
  }
  return false;
};

// 游戏上报（点击游戏下载的时候调用一下） 2023年9月13日14:02:29
export const downloadAdd = async ({ gameId, classId }) => {
  const res = await ApiGameDownloadAdded({
    gameId: gameId,
    classId: classId,
  });
};

// 个人签弹窗处理
export const handleGrqDownload = async detail => {
  // 循环调用接口，更改各个状态
  const res = await ApigrqGameList({ game_id: detail.id });
  switch (parseInt(res.data.list[0].grq_status)) {
    case 0:
      store.commit('game/setGrqStatus', 0);
      setTimeout(handleGrqDownload(detail), 4000);
      break;
    case 1:
      store.commit('game/setGrqStatus', 1);
      setTimeout(handleGrqDownload(detail), 4000);
      break;
    case 2:
      store.commit('game/setDownloadGrqDialogShow', false);
      // 下载游戏
      BOX_openInBrowser({ h5_url: res.data.list[0].grq_down_ip });
      store.commit('game/setGrqStatus', 2);
      break;
    case 3:
    case 4:
      store.commit('game/setDownloadGrqDialogShow', false);
      Toast(i18n.t('签名失败'));
      store.commit('game/setGrqStatus', -1);
      break;
  }
};

// 下载游戏
export const downloadGame = async detail => {
  const checkRes = await checkDownload();
  if (!checkRes) {
    return false;
  }
  downloadAdd({
    gameId: detail.id,
    classId: detail.classid,
  });
  // 安卓下载
  if (!isIos) {
    if (isAndroidBox) {
      // 安卓马甲包
      window.BOX.startDownload(JSON.stringify(detail));
      setTimeout(() => {
        BOX_showActivity({ isAndroidBoxToNative: true }, { page: 'yygl' });
      }, 200);
    } else {
      window.location.href = detail.down_a;
    }
  } else {
    // 引导到safari下
    if (needGuide && !isSafari) {
      store.commit('game/setNoSafariShow', true);
      return false;
    } else {
      // 判断登录
      if (store.getters['user/userInfo'].token) {
        // 如果是个人签
        if (detail.repair) {
          // 走我们自己的下载方法
          if (parseInt(detail.grq_status) === 1) {
            let userAgent = isIosBox ? 2 : isWebApp ? 0 : 1;
            ApiPersonalSign({
              game_id: detail.id,
              from_plat: userAgent,
              packageName: userAgent === 2 ? BOX_getPackageName() : '',
            }).then(res => {
              switch (parseInt(res.data.status)) {
                // 不是svip
                case 2:
                  router.push({ name: 'Svip' });
                  break;
                // 没有绑定udid
                case 3:
                  store.commit('game/setGetUdidPopupShow', true);
                  // 能走到这里的只有ios的马甲包和webapp
                  setTimeout(() => {
                    let { jump_url, mobileconfig_url, mobileprovision_url } =
                      res.data;
                    if (isIosBox) {
                      // 下载配置文件,双url才能跳转到配置文件
                      BOX_openInBrowser({
                        h5_url: `${jump_url}?url1=${mobileconfig_url}&url2=${mobileprovision_url}`,
                      });
                    } else {
                      window.location.href = mobileconfig_url;
                      setTimeout(() => {
                        // 非套壳情况需要第二遍让其自动跳转
                        window.location.href = mobileprovision_url;
                      }, 1000);
                    }
                  }, 200);
                  break;
                // 已经签完
                case 5:
                  store.commit('game/setDownloadPopupShow', true);
                  // 下载游戏
                  BOX_openInBrowser({ h5_url: res.data.grq_down_ip });
                  break;
                default:
                  store.commit('game/setGetUdidPopupShow', false);
                  store.commit('game/setDownloadGrqDialogShow', true);
                  this.handleGrqDownload(detail);
              }
            });
          }
          // 走纸片内测
          else if (parseInt(detail.grq_status) === 2) {
            ApiUserCheckGrqAuthStatus({
              grq_status: detail.grq_status,
              game_id: detail.id,
            }).then(res => {
              // 第三方个人签，纸片内测
              BOX_openInBrowser({ h5_url: res.data.zp_grq_url });
            });
          }
        }
        // 如果是企业签
        else {
          store.commit('game/setDownloadPopupShow', true);
          // 下载游戏
          BOX_openInBrowser({ h5_url: detail.down_ip });
        }
      }
      // 如果没登录
      else {
        router.push({ name: 'PhoneLogin' });
      }
    }
  }
};

// h5游戏下载
export async function downloadH5Game(appid) {
  if (!!isIos) {
    setTimeout(() => {
      // 下载配置文件,双url才能跳转到配置文件
      let jump_url = 'https://grq.3733.com/index/out/url'; // 中转页
      let mobileconfig_url = `https://d2.xz3733.com/h5/${appid}.mobileconfig`; // 下载链接 拼appid
      // let mobileconfig_url = `https://d2.xz3733.com/h5/65177.mobileconfig` // 测试游戏链接
      let mobileprovision_url = 'https://grq.3733.com/app.mobileprovision'; // 跳转系统设置页
      if (platform == 'iosBox') {
        BOX_openInBrowser({
          h5_url: `${jump_url}?url1=${mobileconfig_url}&url2=${mobileprovision_url}`,
        });
      } else {
        window.location.href = mobileconfig_url;
        setTimeout(() => {
          // 非套壳情况需要第二遍让其自动跳转
          window.location.href = mobileprovision_url;
        }, 1000);
      }
    }, 200);
  }
}

export async function startH5Game(h5Url, appid) {
  // title-游戏名
  // 验证token是否过期
  await store.dispatch('user/SET_USER_INFO');
  if (!store.getters['user/userInfo'].token) {
    router.push({ name: 'PhoneLogin' });
  } else {
    store.commit('game/setH5GameUrl', h5Url);
    localStorage.setItem('STORE', JSON.stringify(store.state));
    router.push({ name: 'H5Game', params: { h5Url: h5Url } });
  }
}

export function startCloudGame(id) {
  router.push({ name: 'CloudGame', params: { game_id: id } });
}

export function pay() {
  const payWay = arguments[1].payWay;
  // 打开loading窗口
  const toast1 = Toast.loading({
    message: i18n.t('支付中...'),
    forbidClick: true,
    duration: 0,
  });
  return new Promise((resolve, reject) => {
    // 请求支付接口
    request(...arguments)
      .then(res => {
        toast1.clear();
        let { param_url, alipay_url, url, html, qrSrcUrl } = res.data;
        // 做支付接口成功处理
        switch (payWay) {
          case 'wx': // 微信支付 ----------------------------------------
            if (param_url) {
              window.location.href = param_url;
              Dialog.confirm({
                message: i18n.t('是否支付成功'),
                confirmButtonText: i18n.t('是'),
                cancelButtonText: i18n.t('否'),
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  resolve(res);
                })
                .catch(() => {
                  resolve(res);
                })
                .finally(() => {
                  store.dispatch('user/SET_USER_INFO');
                });
            } else if (url) {
              window.location.href = url;
              setTimeout(() => {
                Dialog.confirm({
                  message: i18n.t('是否支付成功'),
                  confirmButtonText: i18n.t('是'),
                  cancelButtonText: i18n.t('否'),
                  lockScroll: false,
                })
                  .then(() => {
                    // on confirm
                    resolve(res);
                  })
                  .catch(() => {
                    resolve(res);
                  })
                  .finally(() => {
                    store.dispatch('user/SET_USER_INFO');
                  });
              }, 1000);
            }
            break;

          case 'zfb_dmf': // 支付宝支付 ------------------------------------------
            if (alipay_url) {
              BOX_openInBrowser({ h5_url: alipay_url }, { url: alipay_url });
              Dialog.confirm({
                message: i18n.t('是否支付成功'),
                confirmButtonText: i18n.t('是'),
                cancelButtonText: i18n.t('否'),
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  resolve(res);
                })
                .catch(() => {
                  resolve(res);
                })
                .finally(() => {
                  store.dispatch('user/SET_USER_INFO');
                });
            } else if (html) {
              // 如果是有html（即支付宝h5支付类型）
              /* 此处form就是后台返回接收到的数据 */
              const div = document.createElement('div');
              div.setAttribute('class', 'yy-alipay');
              div.innerHTML = res.data.html;
              document.body.appendChild(div);
              document.forms[0].submit();
              setTimeout(() => {
                Dialog.confirm({
                  message: i18n.t('是否支付成功'),
                  confirmButtonText: i18n.t('是'),
                  cancelButtonText: i18n.t('否'),
                  lockScroll: false,
                })
                  .then(() => {
                    // on confirm
                    resolve(res);
                  })
                  .catch(() => {
                    resolve(res);
                  })
                  .finally(() => {
                    store.dispatch('user/SET_USER_INFO');
                    document.getElementsByClassName('yy-alipay')[0].remove();
                  });
              }, 1000);
            }
            break;

          case 'wx_ewm': // 微信二维码支付 ---------------------------------------
          case 'zfb_dmf_ewm': // 支付宝二维码支付 ------------------------------------
            if (!qrSrcUrl) {
              Toast(i18n.t('暂不支持扫码支付'));
              return false;
            }
            setTimeout(() => {
              // 延时器保证二维码在下面的确认框上层(不然会被遮住)
              store.commit('recharge/setShowEwmPopup', true);
              store.commit('recharge/setEwmSrc', qrSrcUrl);
            }, 0);
            Dialog.confirm({
              message: i18n.t('是否支付成功'),
              confirmButtonText: i18n.t('是'),
              cancelButtonText: i18n.t('否'),
              lockScroll: false,
            })
              .then(() => {
                // on confirm
                resolve(res);
              })
              .catch(() => {
                resolve(res);
              })
              .finally(() => {
                store.dispatch('user/SET_USER_INFO');
              });
            break;

          case 'gold': // 金币抵扣 ---------------------------------------
          case 'ptb': // 平台币支付 ------------------------------------
            // 支付方式是平台币或者金币的话直接显示支付结果(isPay字段有值)并跳转支付成功页面
            Toast.success(i18n.t('支付成功'));
            resolve(res);
            break;
          case 'paypal': // paypal支付 ------------------------------------
            if (url) {
              Dialog.confirm({
                message: 'paypal需要跳转到外部支付，是否打开',
                confirmButtonText: i18n.t('是'),
                cancelButtonText: i18n.t('否'),
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: i18n.t('是否支付成功'),
                      confirmButtonText: i18n.t('是'),
                      cancelButtonText: i18n.t('否'),
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 1000);
                  resolve(res);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          case 'mycard': // mycard支付 ------------------------------------
            if (url) {
              Dialog.confirm({
                message: 'mycard需要跳转到外部支付，是否打开',
                confirmButtonText: i18n.t('是'),
                cancelButtonText: i18n.t('否'),
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: i18n.t('是否支付成功'),
                      confirmButtonText: i18n.t('是'),
                      cancelButtonText: i18n.t('否'),
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 1000);
                  resolve(res);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          default:
            break;
        }
        return false;
      })
      .catch(res => {
        // 做支付接口失败处理
        reject(res);
        toast1.clear();
      });
  });
}

// 获取链接所带参数(根据key获取value)
export function getQueryVariable(variable) {
  let query = window.location.search.substring(1);
  let vars = query.split('&');
  for (let i = 0; i < vars.length; i++) {
    let pair = vars[i].split('=');
    if (pair[0] == variable) {
      return pair[1];
    }
  }
  return false;
}
// 判断url是否含有参数
export function hasUrlQuery(url) {
  if (!url) {
    let query = window.location.search.substring(1);
    if (query) return true;
  } else {
    let index = url.indexOf('?');
    if (index != -1) return true;
  }
  return false;
}
// 日志打印
export function devLog() {
  if (
    process.env.NODE_ENV == 'development' ||
    h5Page.env == 'aa' ||
    h5Page.env == 'cc'
  ) {
    window.console.log(...arguments);
  }
}

// 一天中0时的时间戳
export function getDayZeroTimestamp(timestamp) {
  // 秒
  timestamp = Number(timestamp) * 1000;
  return new Date(new Date(timestamp).toLocaleDateString()).getTime();
}

// 点击banner的一项
export function clickBanner(banner) {
  switch (Number(banner.type)) {
    case 1:
      if (banner.game.is_up) {
        this.toPage('UpDetail', {
          id: banner.game.id,
          gameInfo: banner.game,
        });
      } else {
        BOX_goToGame(
          {
            params: {
              id: banner.game.id,
              gameInfo: banner.game,
            },
          },
          { id: banner.game.id },
        );
      }
      break;
    case 4:
      if (banner.extra.indexOf('activity.3733.com') > -1) {
        BOX_openInNewWindow(
          { name: 'Activity', params: { url: banner.extra } },
          { url: banner.extra },
        );
      } else if (banner.page_name) {
        BOX_openInNewNavWindow(
          { name: banner.page_name },
          { url: banner.extra },
        );
      } else {
        BOX_openInNewNavWindow({ h5_url: banner.extra }, { url: banner.extra });
      }
      break;
    case 30: // 捡漏
      BOX_showActivity({ name: 'Jianlou' }, { page: 'jl' });
      break;
    case 21: // 金币商城
      BOX_showActivity({ name: 'GoldMall' }, { page: 'jbsc' });
      break;
    case 9: // 签到
      BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
      break;
    case 8: // 返利申请
      BOX_showActivity({ name: 'Rebate' }, { page: 'fl' });
      break;
    case 22: // 小号回收
      BOX_showActivity({ name: 'Recycle' }, { page: 'xhhs' });
      break;
    case 23: // 转游中心
      BOX_showActivity({ name: 'Zhuanyou' }, { page: 'zyzx' });
      break;
    case 13: // 金币转盘
      BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
      break;
    case 15: // 邀请赚佣金
      BOX_openInNewWindow(
        { name: 'Invite' },
        { url: `${window.location.origin}/#/invite` },
      );
      break;
    case 17: // 排行榜
      BOX_showActivity({ name: 'Rank' }, { page: 'phb' });
      break;
    case 18: // 新游首发
      BOX_showActivity({ name: 'NewGame' }, { page: 'xysf' });
      break;
    case 31: // 合集
      this.toPage('GameCollect', { id: banner.heji_id });
      break;
    case 32: // 游戏试玩
      BOX_showActivity({ name: 'GameTry' }, { page: 'yxsw' });
      break;
    case 33: // 游戏内测员
      BOX_showActivity({ name: 'GameTester' }, { page: 'yxncy' });
      break;
    default:
      break;
  }
}
