import { BOX_getProtocolKey } from '@/utils/box.uni.js';
let protocolKey = BOX_getProtocolKey();

let h5Page = {};
let env = '';
const DOMAIN = 'a3733.com';

env = envFun();
if (process.env.NODE_ENV === 'development') {
  // 在这里调环境 'aa' 'cc' '';
  env = 'aa';
}

const domainUpload = `https://image.3733.com`; //上传头像的接口域名
const api_url = `https://${env}api.${DOMAIN}`; //默认接口域名
const api_url2 = `https://${env}u.${DOMAIN}`; //金币那堆的接口域名
const apigrq = `https://grq.3733.com`; //个人签接口

h5Page.meirirenwu = `https://${env}game.3733.com/#/task_daily`; // 每日任务
h5Page.jinbinduobao = `https://${env}game.3733.com/#/gold_gamble`; // 金币夺宝
h5Page.wodelijin = `https://${env}game.3733.com/#/cash_gift`; // 我的礼金
h5Page.dakatiaozhan = `https://${env}game.3733.com/#/clock_challenge`; // 打卡挑战

h5Page.caifudengji = `https://${env}game.3733.com/#/pay_help`; // 财富等级
h5Page.caifudengjiExp = `https://${env}game.3733.com/#/pay_help_explain`; // 财富等级说明
h5Page.caifudengjiDetail = `https://${env}game.3733.com/#/pay_help_detail`; // 财富等级详情

h5Page.svip_url = `https://${env}api.a3733.com/h5/vip/index?is_new=1`;
h5Page.changwan_url = `https://${env}api.a3733.com/h5/game_card/index`;
h5Page.ptb_url = `https://${env}h5.a3733.com/h5/platcoin/index`;

h5Page.api_url = api_url;
h5Page.api_url2 = api_url2;
h5Page.apigrq = apigrq;
h5Page.domainUpload = domainUpload;

// 无交互类h5的URL
h5Page.daijinquanshuoming = `https://h5.a3733.com/h5/html/couponExplain`;
h5Page.shiyongzhinan = `https://h5.a3733.com/h5/html/useGuide`;
h5Page.mianzeshengming = `https://h5.a3733.com/html/disclaimer/c/${protocolKey}`;
h5Page.yonghuxieyi = `https://h5.a3733.com/html/agreement/c/${protocolKey}`;
h5Page.yinsizhengce = `https://h5.a3733.com/html/privacy/c/${protocolKey}`;
h5Page.fanlizhinan = `https://u.a3733.com/float.php/float/box/rebate_guide.html`;
h5Page.jiaoyixuzhi = `https://${env}h5.a3733.com/h5/html/tradeexplain`;
h5Page.zhuanyoushuoming = `https://h5.a3733.com/h5/zhuanyou/zyShow`;
h5Page.cloudGameQA = `https://m.3733.com/appnews/233272.html`;
h5Page.grqQA = `https://m.3733.com/appnews/234215.html`;
h5Page.qyqQA = `https://m.3733.com/appnews/233275.html`;
h5Page.h5GameQA = `https://m.3733.com/appnews/233273.html`;
h5Page.h5GameDownloadQA = `https://m.3733.com/appnews/311956.html`;
h5Page.upAgreement = `https://${env}api.a3733.com/h5/apk/upAgreement`;
h5Page.hejiguanli = `https://${env}api.a3733.com/html/hjgf`;
h5Page.env = env;

export default h5Page;

// 判断当前处于什么环境
function envFun() {
  let url = window.location.href;
  let arr = url.split('.');
  if (arr[0].indexOf('aa') > -1) {
    return 'aa';
  } else if (arr[0].indexOf('cc') > -1) {
    return 'cc';
  } else {
    return '';
  }
}
