<template>
  <!-- 暂时专门给up主的分类列表页用 -->
  <div class="game-item" @click="toDetail">
    <div class="container">
      <img :src="gameInfo.titlepic" class="game-img" />
      <div class="info">
        <div class="top">{{ gameInfo.title }}</div>
        <div class="center">
          <div
            v-if="gameInfo.app_tag.length > 0 && gameInfo.app_tag[0].type != 50"
            class="tag-list"
          >
            <div
              v-for="(item2, index2) in gameInfo.app_tag"
              :key="index2"
              class="tag-item"
            >
              {{ item2.name }}
            </div>
          </div>
          <div v-else class="type-list">
            <div
              v-for="(item2, index2) in gameInfo.type"
              :key="index2"
              class="type-item"
            >
              {{ item2 }}
            </div>
          </div>
          <div v-if="gameInfo.classid == 40" class="text">
            {{ gameInfo.size_a }}
          </div>
          <UserAvatar
            v-if="gameInfo.up_info"
            :src="gameInfo.up_info.avatar"
            :self="false"
            class="icon"
          />
          <div v-if="gameInfo.up_info" class="name">
            {{ gameInfo.up_info && gameInfo.up_info.nickname }}
          </div>
        </div>
        <div v-if="gameInfo.classid == 40" class="bottom">
          <div class="text">{{ gameInfo.features }}</div>
        </div>
        <div v-if="gameInfo.classid == 41" class="bottom">
          <div class="text">{{ gameInfo.rating.rating }}分</div>
          <div class="text">{{ gameInfo.size_a }}</div>
        </div>
      </div>
      <div @click="toDetail" class="right">试玩</div>
    </div>
  </div>
</template>
<script>
import { BOX_goToGame } from '@/utils/box.uni.js';

export default {
  props: {
    gameInfo: {
      type: Object,
      default: {},
    },
    avatar: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    toDetail() {
      if (this.gameInfo.classid == 41) {
        BOX_goToGame(
          {
            params: {
              id: this.gameInfo.id,
              gameInfo: this.gameInfo,
            },
          },
          { id: this.gameInfo.id },
        );
      } else {
        this.toPage('UpDetail', {
          id: this.gameInfo.id,
          gameInfo: this.gameInfo,
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.game-item {
  height: 64 * @rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  .container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .game-img {
      width: 64 * @rem;
      height: 64 * @rem;
      flex: 0 0 64 * @rem;
      border-radius: 10 * @rem;
    }
    .info {
      flex: 1;
      height: 64 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin: 0 10 * @rem;
      overflow: hidden;
      .top {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14 * @rem;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
      }
      .center {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;
        .tag-list {
          display: flex;
          .tag-item {
            padding: 2 * @rem 4 * @rem;
            background: #f5f5f6;
            border-radius: 4 * @rem;
            font-size: 10 * @rem;
            color: #666;
            white-space: nowrap;
            margin-right: 5 * @rem;
          }
        }
        .type-list {
          display: flex;
          flex-wrap: wrap;
          overflow: hidden;
          height: 17 * @rem;
          margin-right: 12 * @rem;

          .type-item {
            font-size: 12 * @rem;
            color: #929292;
            white-space: nowrap;
            margin-right: 5 * @rem;
            line-height: 17 * @rem;
          }
        }
        .text {
          white-space: nowrap;
          font-size: 10 * @rem;
          color: #999999;
          margin-right: 5 * @rem;
        }
        .name {
          font-size: 10 * @rem;
          color: #666666;
          margin-left: 5 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .icon {
          flex: 0 0 20 * @rem;
          width: 20 * @rem;
          height: 20 * @rem;
          margin-left: 8 * @rem;
        }
      }
      .bottom {
        display: flex;
        overflow: hidden;
        .text {
          font-size: 11 * @rem;
          color: #444444;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          &:nth-of-type(n + 2) {
            margin-left: 5 * @rem;
          }
        }
      }
    }
    .right {
      flex: 0 0 58 * @rem;
      width: 58 * @rem;
      height: 28 * @rem;
      background: #fe6600;
      border-radius: 25 * @rem 25 * @rem 25 * @rem 25 * @rem;
      font-size: 13 * @rem;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
