// box init
import { platform, getToken } from '@/utils/box.uni.js';

import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import i18n from '@/i18n/index.js';
import './api';

import '@/common/styles/reset.css';
import '@/common/styles/border.css';
import '@/common/styles/common.less';

// 全局注册组件
import '@/components';

import '@/utils/datetime.js';

router.beforeEach((to, from, next) => {
  //to即将进入的目标路由对象，from当前导航正要离开的路由， next  :  下一步执行的函数钩子
  if (to.name === 'PhoneLogin' || to.name === 'Login') {
    next();
  } // 如果即将进入登录路由，则直接放行
  else {
    //进入的不是登录路由
    if (to.meta.requiresAuth) {
      switch (platform) {
        case 'ios':
          if (!getToken()) {
            window.webkit.messageHandlers.login.postMessage();
          }
          next();
          break;
        case 'android':
          if (!getToken()) {
            BOX.login();
          }
          next();
          break;
        case 'iosBox':
        case 'androidBox':
        case 'h5':
          if (!store.getters['user/userInfo'].token)
            next({ name: 'PhoneLogin' });
          break;
      }
      next();
    } else {
      next();
    }
  } //如果不需要登录验证，或者已经登录成功，则直接放行
});

// 全局注册变量
import h5Page from '@/utils/h5Page';
Vue.prototype.$h5Page = h5Page;

// 全局混入
import mixins from '@/utils/mixins.js';
Vue.mixin(mixins);

// 解决初始化接口异步返回的问题
Vue.prototype.$onAppFinished = new Promise(resolve => {
  Vue.prototype.$isResolve = resolve;
});

Vue.config.productionTip = false;

new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
}).$mount('#app');
