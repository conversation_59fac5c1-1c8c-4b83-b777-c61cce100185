<template>
  <!-- 创建小号提示弹窗 -->
  <van-dialog
    v-model="show"
    :show-confirm-button="false"
    :close-on-click-overlay="true"
    :lock-scroll="false"
    class="create-dialog"
    :beforeClose="close"
  >
    <div class="logo-icon"></div>
    <div class="dialog-content">
      <div class="title">{{ $t('提示') }}</div>
      <div class="message">{{ $t('请先下载游戏并登录创建游戏账号哦') }}</div>
      <div class="dialog-bottom-bar">
        <div class="cancel btn" @click="close">{{ $t('稍后下载') }}</div>
        <div class="confirm btn" @click="confirm">{{ $t('下载游戏') }}</div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      // 游戏id
      type: Number,
      required: true,
    },
  },
  methods: {
    close() {
      this.$emit('update:show', false);
    },
    confirm() {
      this.$emit('update:show', false);
      this.$nextTick(() => {
        BOX_goToGame(
          {
            params: {
              id: this.id,
            },
          },
          { id: this.id },
        );
      });
    },
  },
};
</script>

<style lang="less" scoped>
.create-dialog {
  width: 244 * @rem;
  background: transparent;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      text-align: center;
      line-height: 25 * @rem;
    }
    .message {
      font-size: 13 * @rem;
      color: #000000;
      font-weight: 400;
      text-align: center;
      margin-top: 19 * @rem;
    }
    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 24 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(96deg, #ff9f00 0%, #fe6600 100%);
        border-radius: 18 * @rem;
      }
    }
  }
}
</style>
