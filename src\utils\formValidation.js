export function checkEmail(str) {
  let check = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (check.test(str)) {
    return true;
  } else {
    return false;
  }
}

export function checkIphone(str) {
  let check = /^[1][0-9]{10}$/;
  if (check.test(str)) {
    return true;
  } else {
    return false;
  }
}

export function checkQQ(str) {
  let check = /^[1-9][0-9]{5,10}$/;
  if (check.test(str)) {
    return true;
  } else {
    return false;
  }
}
