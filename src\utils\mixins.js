import { mapGetters } from 'vuex';
import { BOX_close, platform, authInfo } from './box.uni';
import { getQueryVariable } from '@/utils/function.js';
export default {
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      appName: 'system/appName',
      defaultAvatar: 'system/defaultAvatar',
    }),
    systemName() {
      switch (platform) {
        case 'iosBox':
          return 'iOS商店版';
        case 'android':
          return 'Android官包';
        case 'androidBox':
          return 'Android马甲包';
        default:
          return 'iOS书签版';
      }
    },
    appVersion() {
      switch (platform) {
        case 'android':
          return authInfo.versionCode;
        default:
          return process.env.VUE_APP_versionCode;
      }
    },
    currentCps() {
      if (platform == 'android') {
        return authInfo.channel;
      } else {
        if (getQueryVariable('c')) {
          return `cps${getQueryVariable('c')}`;
        }
        return 'empty';
      }
    },
  },
  methods: {
    toPage(name, params = {}, replace = 0) {
      replace
        ? this.$router.replace({ name: name, params: params })
        : this.$router.push({ name: name, params: params });
    },
    back() {
      if (window.sessionStorage.firstUrl === window.location.href) {
        // 首次进入的页面
        if (['android', 'ios'].includes(platform)) {
          // 原生端关闭窗口--包括sdk
          BOX_close();
        } else {
          // 其他-- 返回首页
          this.toPage('QualitySelect');
        }
      } else {
        this.$router.back();
      }
    },
    openKefu(params = {}) {
      if (!this.userInfo.username) {
        ysf('open', { templateId: '6629718' });
      } else {
        // 需传入的额外数据
        let extraData = [
          {
            key: 'laiyuan',
            label: '来源',
            value: `${this.systemName}_${this.appName}_v${this.appVersion}_注册来源：cps${this.userInfo.agent_id}(${this.userInfo.agent_name})_当前使用：${this.currentCps}`,
          },
        ];
        // 如果是返利申请的按钮
        if (params.from == 'fanli') {
          extraData.push({
            key: 'fanli',
            label: '返利申请',
            value: `${params.gameName}_${params.activityName}`,
          });
        }
        ysf('config', {
          uid: this.userInfo.username,
          name: `${this.userInfo.username}${
            this.userInfo.is_svip ? '(SVIP)' : ''
          }`,
          mobile: this.userInfo.mobile ?? '',
          data: JSON.stringify(extraData),
          groupid: this.userInfo.is_svip ? 482269080 : 482171132,
          robotShuntSwitch: 1,
          success: function () {
            ysf('open', { templateId: '6629718' });
          },
        });
      }
    },
  },
};
