<template>
  <!-- 查询可用游戏 -->
  <van-dialog
    v-model="show"
    :lock-scroll="false"
    :show-confirm-button="false"
    :close-on-click-overlay="true"
    class="dialog"
  >
    <div class="search-container">
      <div class="close-search" @click="close"></div>
      <div class="search-bar">
        <div class="input-text">
          <form @submit.prevent="searchGame">
            <input
              type="text"
              v-model.trim="inputGame"
              :placeholder="$t('输入游戏名')"
            />
          </form>
        </div>
        <div class="search-btn" @click="searchGame">{{ $t('搜索') }}</div>
      </div>
      <yy-list
        class="yy-list game-list"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="!gameList.length"
        :tips="$t('请输入您想搜索的游戏')"
        :check="false"
      >
        <div
          class="game-item btn"
          v-for="item in gameList"
          :key="item.id"
          @click="toGame(item)"
        >
          <div class="game-icon">
            <img :src="item.titlepic" alt="" />
          </div>
          <div class="right">
            <div class="game-name">{{ item.title }}</div>
            <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
              {{ item.use_gold_pay == 1 ? '' : $t('不')
              }}{{ $t('支持使用金币抵扣') }}
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </van-dialog>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      gameList: [], // 非金币抵扣的游戏列表
      inputGame: '',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
    };
  },
  methods: {
    close() {
      this.$emit('update:show', false);
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    async searchGame() {
      if (!this.inputGame) {
        this.$toast(this.$t('请输入游戏名'));
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: this.$t('拼命加载中...'),
      });
      this.gameList = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.inputGame,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.gameList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dialog {
  overflow: unset;
  width: 320 * @rem;
}

.search-container {
  box-sizing: border-box;
  width: 320 * @rem;
  height: 450 * @rem;
  padding: 24 * @rem 19 * @rem 10 * @rem;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: unset;
  .close-search {
    width: 24 * @rem;
    height: 24 * @rem;
    background: url(~@/assets/images/recharge/close-search.png) center center
      no-repeat;
    background-size: 24 * @rem 24 * @rem;
    position: absolute;
    right: -10 * @rem;
    top: -10 * @rem;
  }
  .search-bar {
    display: flex;
    align-items: center;
    .input-text {
      width: 240 * @rem;
      height: 35 * @rem;
      border: 1px solid #e5e5e5;
      border-radius: 18 * @rem;
      flex: 1;
      overflow: hidden;
      form {
        display: block;
        width: 100%;
        height: 100%;
      }
      input {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 0 18 * @rem;
        font-size: 15 * @rem;
        color: #333333;
        background-color: #f6f6f6;
      }
    }
    .search-btn {
      font-size: 15 * @rem;
      color: #666666;
      padding-left: 13 * @rem;
      height: 35 * @rem;
      line-height: 35 * @rem;
    }
  }
  .game-list {
    flex: 1;
    overflow: auto;
    margin-top: 10 * @rem;
    .game-item {
      display: flex;
      align-items: center;
      padding: 21 * @rem 0;
      border-bottom: 1px solid #eeeeee;
      .game-icon {
        width: 50 * @rem;
        height: 50 * @rem;
        border-radius: 10 * @rem;
        background-color: #b5b5b5;
      }
      .right {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .game-name {
          font-size: 16 * @rem;
          font-weight: bold;
          color: #000000;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .use-card {
          font-size: 12 * @rem;
          color: #f72e2e;
          margin-top: 10 * @rem;
          &.can {
            color: #36b150;
          }
        }
      }
    }
  }
}
</style>
