import CryptoJS from 'crypto-js';
import { JSEncrypt } from 'jsencrypt';
import <PERSON><PERSON> from 'pako';

// 旧
// const PUBLIC_KEY = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDKFTVRiCqgSbvgoM9NWCPX9tnr\n84u17yJE2jHBesklejdbtSTtiB1JvnWQW032mDmUQnH+oxkCb4+ys/Q/zp/njYDj\nkmUwJE6A7PPZQyDhyBhAeLEbDws6AwiYUyITUq62vpDpuIYjRO0DhJFSuCM5sg6Y\nrdHHo0qN+OSIa2wgAQIDAQAB`;
// 新
const PUBLIC_KEY = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCmhp7mF5m6COjZZbt7nxAfLcGmTbv4gpa3UyAoUBzZpeKwNzXv3nG8xdHtyS0wSKzec5HzJaRkLo7uwuTV6xbBe5qKaC9GjiVioyIfnZdah/K3O8QUfweSC+m+zdQ//SlIvHqKjuTeHmKmMw4trzgKZh8wZoCItar4caX4VvlLQwIDAQAB`;
const RSA_ENCRYPT = new JSEncrypt();
RSA_ENCRYPT.setPublicKey(PUBLIC_KEY);

export function initKeyIv() {
  let key = CryptoJS.lib.WordArray.random(16).toString(CryptoJS.enc.Hex);
  let iv = CryptoJS.lib.WordArray.random(8).toString(CryptoJS.enc.Hex);
  return [key, iv];
}

// rsa加密密码
export function rsaEncrypt(data) {
  const str = RSA_ENCRYPT.encrypt(data);
  return str;
}

// 加密压缩转码
export function encrypt(data, key, iv) {
  // RSA加密key、iv
  const RSAKEY = rsaEncrypt(key);
  const RSAIV = rsaEncrypt(iv);
  // 压缩转为JSON的数据，再创建buffer对象转码base64
  data = Buffer.from(Pako.deflateRaw(JSON.stringify(data))).toString('base64');
  // AES加密BASE64转码（以UTF8格式转化key、iv成wordArray）
  data = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(key), {
    iv: CryptoJS.enc.Utf8.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  }).toString();
  return { data: data, key: RSAKEY, iv: RSAIV };
}

// 解码解密解压
export function decrypt(data, key, iv) {
  // 解码解密、解key和iv、转成wordArray
  data = CryptoJS.AES.decrypt(data, CryptoJS.enc.Utf8.parse(key), {
    iv: CryptoJS.enc.Utf8.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  }).toString(CryptoJS.enc.Latin1);
  // 解压
  data = Pako.inflateRaw(Buffer.from(data, 'base64'), { to: 'string' });
  return data;
}
