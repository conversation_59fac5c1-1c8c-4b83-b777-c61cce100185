import { request } from '../index';
import h5Page from '@/utils/h5Page.js';

/**
 * 搜索
 * @param channel
 * @param build
 */
export function ApiIndexExtra(params = {}) {
  return request('/api/index/extra', params);
}

/* 
上传图片
*/
export function ApiUploadImage(params = {}) {
  return request('/upload', params, false, h5Page.domainUpload);
}

/**
 * 活动列表
 * @param {showType} 1=banner列表  2=web热门活动
 * @return "data": {
  *    "list": [
  *      {
#        "id": 1084,
#        "titleimg": "https://pic5.pic3733.com/banner/202109/110e15d14a4267e65ff6f38d45d0fbd2_n.jpg",
#        "type": 4,  // 1=游戏，2=礼包，3=资讯，4=盒子内部打开url，5=外部浏览器打开url，6=回复，7=预约, 30=捡漏 21=限时兑换
#        "extra": "https://game.3733.com",//额外参数，不同type不同用法
#        "scale": "1",
#        "color_value": "#59676d" //色值
#      }
  *    ]
  *  }
*/
export function ApiIndexBannerList(params = {}) {
  return request('/api/index/bannerList', params);
}

/**
 * up资源功能 - 获取广告配置信息
 * */
export function ApiIndexGetAd(params = {}) {
  return request('/api/index/getAd', params);
}

// up资源功能 - 上传apk文件
/**
 * @param {string} up_file 必填，apk文件
 */
export function ApiUploadApk(params = {}) {
  return request('/h5/apk/upAPk', params, false);
}

// 关于我们
export function ApiCommonAboutus(params = {}) {
  return request('/api/common/aboutUs', params, false);
}
