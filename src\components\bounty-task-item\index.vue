<template>
  <div
    class="bounty-item"
    :class="{ gray: taskInfo.record_status == 3 }"
    @click="goToGame"
  >
    <div class="bounty-card">
      <div class="game-bar">
        <div class="game-icon">
          <img :src="taskInfo.game_titlepic" alt="" />
        </div>
        <div class="game-info">
          <div class="game-title">
            {{ taskInfo.game_title }} <span>{{ taskInfo.game_subtitle }}</span>
          </div>
          <div class="game-extra">
            <div class="info-left">
              <div class="star">
                <div class="star-text">难度：</div>
                <div class="star-list">
                  <div
                    class="star-item"
                    :class="{ on: index < taskInfo.difficulty }"
                    v-for="(star, index) in 5"
                    :key="index"
                  ></div>
                </div>
              </div>
              <div class="reward">
                金币奖励：<span>{{ taskInfo.gold }}金币</span>
              </div>
            </div>

            <div
              class="btn bounty-btn"
              v-if="taskInfo.record_status == 0"
              @click.stop="takeTask"
            >
              领取任务
            </div>
            <div
              class="btn bounty-btn"
              v-if="taskInfo.record_status == 1"
              @click.stop="submitTask"
            >
              提交任务
            </div>
            <div class="btn bounty-btn gray" v-if="taskInfo.record_status == 2">
              已完成
            </div>
            <div class="btn bounty-btn gray" v-if="taskInfo.record_status == 3">
              已过期
            </div>
          </div>
        </div>
      </div>
      <div class="bounty-line">
        <div class="time-bar" v-if="taskInfo.record_status == 1">
          <div class="time-text"> 活动剩余时间： </div>
          <div class="time-clock">
            <div class="num-box">{{ format_residue_time.hour }}</div>
            :
            <div class="num-box">{{ format_residue_time.minute }}</div>
            :
            <div class="num-box">{{ format_residue_time.second }}</div>
          </div>
        </div>
        <div class="time-bar" v-else>
          <div class="time-text">
            时限要求：自领取后{{ taskInfo.limited_time / 3600 }}小时内完成
          </div>
        </div>
        <div class="left">
          剩余：<span>{{ taskInfo.residue }}%</span>
        </div>
      </div>
    </div>
    <div class="bounty-task">
      <span>任务要求：</span>{{ taskInfo.describe }}
    </div>
  </div>
</template>

<script>
/**
 * 任务状态：0未领取 1未完成 2已完成 3已过期
 */
import {
  ApiBountyTaskGetTask,
  ApiBountyTaskGetMyTaskReward,
} from '@/api/views/bounty.js';
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  name: 'bountyTaskItem',
  props: {
    info: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      taskInfo: {},
      timer: null,
    };
  },
  computed: {
    format_residue_time() {
      const timestamp = this.taskInfo.residue_time;

      let second = timestamp % 60;
      let minute = Math.floor(timestamp / 60) % 60;
      let hour = Math.floor(Math.floor(timestamp / 60) / 60);

      second = second < 10 ? '0' + second : second;
      minute = minute < 10 ? '0' + minute : minute;
      hour = hour < 10 ? '0' + hour : hour;

      return { hour, minute, second };
    },
  },
  watch: {
    'taskInfo.record_status': {
      handler(newVal, oldVal) {
        if (this.taskInfo.record_status === 1) {
          this.refreshTimer();
        } else if (this.timer) {
          clearInterval(this.timer);
          this.timer = null;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.taskInfo = this.info;
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
  methods: {
    refreshTimer() {
      clearInterval(this.timer);
      this.timer = null;
      this.timer = setInterval(() => {
        this.taskInfo.residue_time--;
      }, 1000);
    },
    // 领取任务
    async takeTask() {
      this.$toast.loading('加载中');
      const res = await ApiBountyTaskGetTask({
        id: this.taskInfo.id,
      });
      this.taskInfo.record_status = res.data.status;
      this.taskInfo.record_id = res.data.id;
      this.taskInfo.residue_time = this.taskInfo.limited_time;
    },
    async submitTask() {
      this.$toast.loading('加载中');
      const res = await ApiBountyTaskGetMyTaskReward({
        id: this.taskInfo.record_id,
      });
      this.taskInfo.record_status = res.data.status;
    },
    goToGame() {
      BOX_goToGame(
        {
          params: {
            id: this.taskInfo.game_id,
          },
        },
        { id: this.taskInfo.game_id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.bounty-item {
  box-sizing: border-box;
  width: 329 * @rem;
  height: 189 * @rem;
  margin: 20 * @rem auto 0;
  background-color: #f8f8fa;
  border-radius: 8 * @rem;
  overflow: hidden;
  &:first-of-type {
    margin-top: 0;
  }
  &.gray {
    filter: grayscale(100%);
    opacity: 0.95;
  }

  &:first-of-type {
    margin-top: 8 * @rem;
  }
  .bounty-card {
    box-sizing: border-box;
    padding: 18 * @rem 12 * @rem 18 * @rem 12 * @rem;
    .game-bar {
      display: flex;
      align-items: center;
      .game-icon {
        width: 72 * @rem;
        height: 72 * @rem;
        border-radius: 8 * @rem;
      }
      .game-info {
        flex: 1;
        min-width: 0;
        margin-left: 8 * @rem;
        .game-title {
          width: 100%;
          font-size: 14 * @rem;
          color: #333333;
          font-weight: 600;
          line-height: 19 * @rem;
          display: flex;
          align-items: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          span {
            display: block;
            box-sizing: border-box;
            background-color: #f5f5f6;
            border: 1 * @rem solid rgba(0, 0, 0, 0.1);
            height: 17 * @rem;
            line-height: 17 * @rem;
            padding: 0 4 * @rem;
            font-size: 10 * @rem;
            color: #808080;
            border-radius: 4 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 6 * @rem;
          }
        }
        .game-extra {
          display: flex;
          align-items: center;
          .info-left {
            flex: 1;
            min-width: 0;
            .star {
              display: flex;
              align-items: center;
              margin-top: 13 * @rem;
              .star-text {
                font-size: 12 * @rem;
                color: #777777;
              }
              .star-list {
                display: flex;
                align-items: center;
                .star-item {
                  margin: 0 1 * @rem;
                  width: 14 * @rem;
                  height: 14 * @rem;
                  background-image: url(~@/assets/images/welfare/star.png);
                  background-size: 14 * @rem 14 * @rem;
                  &.on {
                    background-image: url(~@/assets/images/welfare/star-on.png);
                  }
                }
              }
            }
            .reward {
              font-size: 10 * @rem;
              color: #777777;
              line-height: 17 * @rem;
              margin-top: 9 * @rem;
              span {
                font-size: 12 * @rem;
                color: #fe6600;
                font-weight: 600;
              }
            }
          }

          .bounty-btn {
            width: 78 * @rem;
            height: 32 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6 * @rem;
            background-color: #fe6600;
            font-size: 14 * @rem;
            color: #ffffff;
            margin-top: 20 * @rem;
            &.gray {
              background: #d8d7e0;
            }
          }
        }
      }
    }
  }
  .bounty-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15 * @rem;
    height: 24 * @rem;
    .time-bar {
      display: flex;
      align-items: center;
      .time-text {
        font-size: 12 * @rem;
        color: #777777;
      }
      .time-clock {
        display: flex;
        align-items: center;
        font-size: 16 * @rem;
        color: #fe6600;
        .num-box {
          width: 24 * @rem;
          height: 24 * @rem;
          background-color: #ffe2cf;
          border-radius: 3 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15 * @rem;
          font-weight: bold;
          color: #fe6600;
          margin: 0 8 * @rem;
        }
      }
    }

    .left {
      width: 78 * @rem;
      text-align: center;
      font-size: 12 * @rem;
      color: #777777;
      span {
        color: #fe6600;
        font-weight: 600;
      }
    }
  }
  .bounty-task {
    font-size: 13 * @rem;
    color: #666666;
    height: 42 * @rem;
    line-height: 42 * @rem;
    background-color: #f1f2fa;
    padding: 0 16 * @rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    span {
      color: #333333;
      font-weight: 600;
    }
  }
}
</style>
