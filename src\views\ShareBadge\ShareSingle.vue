<template>
  <div class="share-single-page">
    <rubber-band :topColor="'#151412'" :bottomColor="'#151412'">
      <ShareDownBox />
      <div
        class="single-container"
        :class="{
          centered: !isLoading,
        }"
      >
        <loading-indicator v-if="!isLoading" />

        <template v-else>
          <div class="share-user">
            <div
              class="user-info"
              v-if="shareUserInfo && Object.keys(shareUserInfo).length > 0"
            >
              <div class="user-avatar">
                <img
                  v-if="shareUserInfo.avatar"
                  :src="shareUserInfo.avatar"
                  alt=""
                />
                <img v-else :src="defaultAvatar" alt="" />
              </div>
              <div class="user-name">{{ shareUserInfo.nickname }}</div>
            </div>
          </div>
          <div
            class="main"
            :class="{
              centered:
                (badgeList &&
                  Object.keys(badgeList).length == 1 &&
                  currentBadge.record_id == 0) ||
                !Object.keys(badgeList).length,
            }"
          >
            <div
              class="empty-box"
              v-if="
                (badgeList &&
                  Object.keys(badgeList).length == 1 &&
                  currentBadge.record_id == 0) ||
                !Object.keys(badgeList).length
              "
            >
              <default-not-found-page notFoundText="没有该徽章哦～" />
            </div>
            <template v-else>
              <div class="badge-swiper">
                <div class="shadow-bg"> </div>
                <swiper
                  class="badge-content"
                  :options="swiperBannerOptions"
                  ref="badgeSwiper"
                >
                  <swiper-slide
                    class="swiper-slide"
                    v-for="(item, index) in badgeList"
                    :key="item.id"
                    :class="{ 'is-active-slide': currentIndex === index }"
                  >
                    <div class="swiper-item">
                      <img :src="item.icon_url" alt="" />
                    </div>
                  </swiper-slide>
                </swiper>
              </div>
              <div class="badge-info">
                <div class="content">
                  <div class="content-info">
                    <div class="title">{{ currentBadge.name }}</div>
                    <div class="number"
                      >已有{{ currentBadge.have_num }}人 获得</div
                    >
                  </div>
                </div>
                <div class="require" v-if="currentBadge.arrive_text">
                  <div
                    class="require-text"
                    v-html="currentBadge.arrive_text"
                  ></div>
                </div>
                <div class="create-time">
                  <div class="time" v-if="currentBadge.create_time"
                    >{{ formatDate(currentBadge.create_time) }} 获得该徽章</div
                  >
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
      <div class="share-btn">
        <span class="btn" @click="handleDownload()">去解锁我的第一块徽章</span>
      </div>
    </rubber-band>
  </div>
</template>

<script>
import ShareDownBox from './components/share-down-box.vue';
import LoadingIndicator from '@/components/loading-Indicator';
import { ApiUserBadgeShare } from '@/api/views/badgeCollection.js';
export default {
  name: 'ShareSingle',
  props: {},
  components: {
    ShareDownBox,
    LoadingIndicator,
  },
  data() {
    return {
      title: '',
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      layerBoxOpacity: 1,
      swiperBannerOptions: {
        slidesPerView: 2,
        centeredSlides: true,
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerGroup: 1, // 一次滑动一个
        slideToClickedSlide: true,
        spaceBetween: 20,
        loop: false,
        resistanceRatio: 0, // 不允许边界回弹
        observer: true,
        observeSlideChildren: true,
      },
      badgeList: [],
      currentBadge: {},
      shareUserInfo: {},
      currentIndex: 0,
      isLoading: false,
      params: {
        id: 0,
        share_type: 14,
        record_id: 0,
        mem_id: 0,
      },
    };
  },
  created() {
    this.params.id = this.$route.query.id;
    this.params.record_id = this.$route.query.record_id || 0;
    this.params.mem_id = Number(this.$route.query.mem_id) || 0;
    this.getUserBadgeShare();
  },
  mounted() {},
  methods: {
    handleDownload() {
      window.location.href = 'https://app.3733.com/';
    },
    formatDate(val) {
      let { year, month, day } = this.$handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },
    async getUserBadgeShare() {
      try {
        const res = await ApiUserBadgeShare({
          id: this.params.id,
          share_type: this.params.share_type,
          record_id: this.params.record_id,
          mem_id: this.params.mem_id,
        });
        this.currentIndex = 0;
        this.badgeList = [res.data.badge] || [];
        this.currentBadge = res.data.badge || {};
        this.shareUserInfo = res.data.user || {};
      } catch (error) {
      } finally {
        this.isLoading = true;
      }
    },
  },
  beforeDestroy() {},
};
</script>

<style lang="less" scoped>
.share-single-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #151412 0%, #151412 100%);
  display: flex;
  /deep/.rubber-band {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  @keyframes float-swiper {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-6px);
    }
    100% {
      transform: translateY(0);
    }
  }
  .top-bg {
    width: 100%;
    height: 358 * @rem;
    background: url(~@/assets/images/badge-collection/badge-collection-bg2.png)
      no-repeat top right;
    background-size: 100% auto;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    .layer-box {
      position: relative;
      z-index: 5;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 127 * @rem;
      height: 127 * @rem;
      transition: opacity 0.3s ease;
      .myParticleCanvas {
        width: 100%;
        height: 100%;
      }
    }
  }
  .single-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    &.centered {
      display: flex;
      flex-direction: column;
      align-content: center;
      justify-content: center;
    }
    .share-user {
      position: relative;
      padding-top: calc(104 * @rem + @safeAreaTop);
      padding-top: calc(104 * @rem + @safeAreaTopEnv);
      display: flex;
      align-items: center;
      justify-content: center;
      .user-info {
        display: flex;
        align-items: center;
        justify-content: center;
        .user-avatar {
          width: 46 * @rem;
          height: 46 * @rem;
          border: 2px solid rgba(255, 255, 255, 0.4);
          border-radius: 50%;
          overflow: hidden;
          background: #ccc;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .user-name {
          margin-left: 8 * @rem;
          font-weight: bold;
          font-size: 16 * @rem;
          color: #fffcec;
        }
      }
    }
    .main {
      flex: 1;
      padding-bottom: 30 * @rem;
      &.centered {
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;
      }
      .badge-swiper {
        position: relative;
        margin-top: 51 * @rem;
        .shadow-bg {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 110 * @rem;
          width: 219 * @rem;
          height: 102 * @rem;
          background: url(~@/assets/images/badge-collection/badge-shadow-bg.png)
            no-repeat center center;
          background-size: 219 * @rem 102 * @rem;
        }
        .badge-content {
          .swiper-slide {
            width: 100 * @rem;
            height: 100 * @rem;
            transition: all 0.3s;
            opacity: 0.5;
            &:not(:last-child) {
              margin-right: 20 * @rem;
            }
            .swiper-item {
              width: 100%;
              height: 100%;
              img {
                width: 100%;
                height: 100%;
              }
            }
          }
          /deep/.swiper-slide-active {
            .swiper-item {
              animation: float-swiper 4s ease-in-out infinite;
            }
          }
          .is-active-slide {
            width: 170 * @rem !important;
            height: 170 * @rem !important;
            opacity: 1 !important;
          }
        }
      }
      .badge-info {
        margin-top: 29 * @rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        .content {
          display: flex;
          align-items: center;
          &::before {
            content: '';
            display: block;
            width: 30 * @rem;
            height: 44 * @rem;
            margin-right: 16 * @rem;
            margin-top: 7 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon5.png)
              no-repeat;
            background-size: 30 * @rem 44 * @rem;
          }
          &::after {
            content: '';
            display: block;
            width: 30 * @rem;
            height: 44 * @rem;
            margin-left: 16 * @rem;
            margin-top: 7 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon6.png)
              no-repeat;
            background-size: 30 * @rem 44 * @rem;
          }
          .content-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            .title {
              height: 31 * @rem;
              line-height: 31 * @rem;
              font-weight: 600;
              font-size: 22 * @rem;
              color: #ffffff;
            }
            .number {
              margin-top: 3 * @rem;
              font-weight: 400;
              font-size: 13 * @rem;
              color: rgba(255, 255, 255, 0.3);
            }
          }
        }
        .require {
          width: 280 * @rem;
          line-height: 21 * @rem;
          margin-top: 15 * @rem;
          font-weight: 400;
          font-size: 15 * @rem;
          color: rgba(255, 255, 255, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          &::before {
            flex-shrink: 0;
            content: '';
            display: block;
            width: 16 * @rem;
            height: 12 * @rem;
            margin-right: 6 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon1.png)
              no-repeat;
            background-size: 16 * @rem 12 * @rem;
          }
          &::after {
            flex-shrink: 0;
            content: '';
            display: block;
            width: 16 * @rem;
            height: 12 * @rem;
            margin-left: 6 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon2.png)
              no-repeat;
            background-size: 16 * @rem 12 * @rem;
          }
          .require-text {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .time {
          margin-top: 5 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  .share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 116 * @rem;
    span {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 46 * @rem;
      background: linear-gradient(90deg, #fcf7d1 0%, #f7d7b0 100%);
      border-radius: 23 * @rem;
      font-weight: bold;
      font-size: 16 * @rem;
      color: #a25100;
      white-space: nowrap;
      padding: 0 45 * @rem;
    }
  }
}
</style>
