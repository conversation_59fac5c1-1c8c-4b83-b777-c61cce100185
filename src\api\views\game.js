import { request } from '../index';
import h5Page from '@/utils/h5Page';

export function ApiGameCate(params = {}) {
  return request('/api/game/cate', params);
}

export function ApiGameIndex(params = {}) {
  return request('/api/game/index', params);
}

// 游戏详情
export function ApiGameRead(params = {}) {
  return request('/api/game/read', params);
}

// 游戏详情开服
export function ApiServerIndex(params = {}) {
  return request('/api/server/index', params);
}

// 开服提醒/取消提醒
/**
 * @param serverId 开服id sdkServer中id
 * @param status 提醒状态 1开启提醒 0取消提醒
 */
export function ApiServerRemind(params = {}) {
  return request('/api/server/remind', params);
}

export function ApiGameRecommend(params = {}) {
  return request('/api/game/recommend', params);
}

// 我的游戏
export function ApiUserDownloadedGame(params = {}) {
  return request('/api/user/downloadedGame', params);
}

/**
 * 重签游戏
 * @param game_id
 * @param udid
 * @param status 当前签名包状态 0:队列中，1:正在签名，2：签名成功，3：签名失败，4：文件冲突暂停签名
 * @param is_update 尊享版盒子更新传1，其余情况不传
 * @param channel 渠道号（用在签尊享版盒子时传入，其余情况不传）
 */

export function ApiRestGrqGame(params = {}) {
  return request('/index/api/resignGame/', params, false, h5Page.apigrq);
}

/**
 * 个人签游戏列表
 * @param game_id
 * @param uuid
 * @param udid
 * res
 * grq_status: 0队列中、1正在签名、2签名成功、3签名失败、4文件冲突暂停签名
 */
export function ApigrqGameList(params = {}) {
  return request('/index/api/gameList/', params, false, h5Page.apigrq);
}

/**
 * 获取云游戏信息
 * @param gameId 游戏ID
 * #"data": {
#    "bid": "51e2540b4fe", //用于SDK实例化
#    "pkg_name": "com.netease.moba", //包名
#    "game_name": "决战平安京", //游戏名
#    "app_channel": "3733", //渠道号
#    "user_id": 210205, //
#    "user_token": "wb3ba12e3yi4ubjr",
#    "c_token": "0182310f06791578a2a0686eee3f78acd39606bd",
#    "priority": 0,
#    "play_Time": 300,
#    "portrait": 0,
#    "config": "",
#    "once_token": "0662ac09baff85e93429cf56793750af" //用于云游戏"免登录"的一次性token
#}
*/
export function ApiCloudGameInfo(params = {}) {
  return request('/api/yyx/hmGameInfo', params);
}

/**
 * 验证游戏支付订单信息
 * @param payUrl 游戏内部返回的链接
##return
#orderInfo信息
#      "mem_id": 5533639,//用户id
#      "amount": "30.00",//充值金额
#      "original_price": "0.00",//原价
#      "isFirstPay": 0,//是否首充 1：是
#      "pay_rebate": 100,// 折扣
#      "itemName": "充值30元",
#      "username": "u73460234",//用户名
#      "ptbBal": 4077,//平台币余额
#      "goldBal": 14179//金币余额
#       "coupon": {//代金券信息
#        "money": 0,
#        "max_money": 0,
#        "coupon_count": 0,//代金券可用张数
#        "coupon_record_id": 0,//使用的代金券
#        "cardshow": 1
#      },
#}
*/
export function ApiGetGamePayInfo(params = {}) {
  return request('/web/game_pay/index', params);
}

// 不可用金币充值的游戏
export function ApiGoldcoinUnusableGame(params = {}) {
  return request('/web/goldcoin/unusableGame', params);
}

/**
 * 获取游戏权限
 * @param id 游戏id
 *
 */
export function ApiGameGetPermissionInfo(params = {}) {
  return request('/api/game/getPermissionInfo', params);
}

// 收藏
/**
 * @param classId 1=资讯，2=动态，3=应用，4=推荐，5=交易小号，6=捡漏小号，101=资讯评论，102=动态评论，103=应用评论, 104=推荐评论
 * @param sourceId 资源ID：status为-1时支持批量取消（以英文逗号隔开）
 * @param status -1=取消收藏，1=添加收藏
 */
export function ApiResourceCollect(params = {}) {
  return request('/api/resource/collect', params);
}

// 收藏状态
/**
 * @param order 3
 * @param classId 1=资讯，2=动态，3=应用，4=推荐，5=交易小号，6=捡漏小号，101=资讯评论，102=动态评论，103=应用评论, 104=推荐评论
 * @param sourceId 资源id
 * @return {collection_status} -1=取消收藏，1=添加收藏 0 =未收藏
 */
export function ApiResourceCollectStatus(params = {}) {
  return request('/api/resource/collStatus', params);
}

// 我的收藏
/**
 * @param order 3
 * @param classId 1=资讯，2=动态，3=应用，4=推荐，5=交易小号，6=捡漏小号，101=资讯评论，102=动态评论，103=应用评论, 104=推荐评论
 * @return {status} 1 = 已收藏 0 =已取消收藏
 */
export function ApiResourceCollection(params = {}) {
  return request('/api/resource/collection', params);
}

// 新游首发/新游一周top
/**
 * @param type 1新游首发 2新一周top10
 */
export function ApiGameNewGameList(params = {}) {
  return request('/api/game/newGameList', params);
}

// 新游首发/新游一周top
/**
 * @param type 1新游首发 2新一周top10
 */
export function ApiGameNewGameListV2(params = {}) {
  return request('/api/game/newGameListV2', params);
}

// 新游预约
export function ApiGameSubscribeGames(params = {}) {
  return request('/api/game/subscribeGames', params);
}

// 新游预约
/**
 * @param {gameId} 游戏id
 */
export function ApiGameSubscribe(params = {}) {
  return request('/api/game/subscribe', params);
}

// 0元畅玩
export function ApiGameZeroGame(params = {}) {
  return request('/api/game/zeroGame', params);
}

// 游戏排行榜单 - tab栏目
export function ApiGameTabRanking(params = {}) {
  return request('/api/game/tabRanking', params);
}

// 游戏 - up游戏排行榜单 - 栏目
export function ApiGameTabUpRanking(params = {}) {
  return request('/api/game/tabUpRanking', params);
}

// 游戏排行榜单 -- webapp
/**
 * @param {type} 榜单类型 tabRanking接口中的order字段
 */
export function ApiGameWebRanking(params = {}) {
  return request('/api/game/webRanking', params);
}

// 合集列表
/**
 * @param {id} 合集id
 */
export function ApiCollectGameCollect(params = {}) {
  return request('/api/collect/gameCollect', params);
}

// 查询不可使用代金券游戏
export function ApiGameCardGetGameAjax(params = {}) {
  return request('/web/game_card/getGameAjax', params);
}

// 玩家推荐
export function ApiGameGetRecommendList(params = {}) {
  return request('/api/game/getRecommendList', params);
}

// 获取用户推荐详情
/**
 * @param {gameId} 游戏id 传gameId的时候是拿该游戏的第一个推荐的详情
 * @param {id} 推荐id 一般传id就行了
 */
export function ApiGameGetRecommendInfo(params = {}) {
  return request('/api/game/getRecommendInfo', params);
}

// 获取用户推荐详情
/**
 * @param {reId} 推荐ID
 * @param {type} 0=取消点赞 1=点赞
 */
export function ApiGameRecommendThumb(params = {}) {
  return request('/api/game/recommendThumb', params);
}
// 游戏下载check
export function ApiGameCheckDown(params = {}) {
  return request('/api/game/checkDown', params);
}

// 顶部分类
export function ApiGameGetGameClassList(params = {}) {
  return request('/api/game/getGameClassList', params);
}

// 分类 4.4
export function ApiGameGetCateTheme(params = {}) {
  return request('/api/game/getCateTheme', params);
}

// 分类 4.4 --游戏列表
/**
 * @param {type} 分类传
 * @param {theme} 题材传
 */
export function ApiGameIndexV1(params = {}) {
  return request('/api/game/index_v1', params);
}

// --up资源功能 - 提交求更新
/**
 * @param {remark} 内容
 */
export function ApiGameSubmitCrackWish(params = {}) {
  return request('/api/game/submitCrackWish', params);
}

// up资源功能 - 收藏up资源游戏
/**
 * @param {type} 1收藏0取消
 * @param {gameId}
 */
export function ApiGameCollectionUpGame(params = {}) {
  return request('/api/game/collectionUpGame', params);
}

// up资源功能 - 获取游戏分类
export function ApiGameGetUpCate(params = {}) {
  return request('/api/game/getUpCate', params);
}

// up资源功能 - up游戏评价 鲜花臭鸡蛋
/**
 * @param {string} gameId 游戏ID
 * @param {string} type 1鲜花2臭鸡蛋
 */
export function ApiUpGameEvaluate(params = {}) {
  return request('/api/game/upGameEvaluate', params);
}

// 获取内测列表
export function ApiInternalTestList(params = {}) {
  return request('/api/internal_test/getList', params);
}

// 游戏内测 - 领取全部内测代金券
export function ApiInternalTestCoupon(params = {}) {
  return request('/api/internal_test/getTextCoupon', params);
}

// 游戏内测 - 用户假数据
export function ApiInternalTestMemList(params = {}) {
  return request('/api/internal_test/getTestMemList', params);
}

// 游戏内测 - 参加内测
export function ApiInternalParticipateTest(params = {}) {
  return request('/api/internal_test/participateTest', params);
}

// up资源功能 - up游戏库列表
/**
 */
export function ApiUpGameIndex(params = {}) {
  return request('/api/game/upGameIndex', params);
}

// up资源功能 - up游戏库列表
export function ApiUpGameSpMore(params = {}) {
  return request('/api/game/upSpMore', params);
}

// up资源功能 - up游戏库列表
export function ApiGameGetExchangeCode(params = {}) {
  return request('/api/game/getExchangeCode', params);
}

/**
 * @function 下载游戏上报
 * @param {number} gameId
 * @param {number} classId
 */
export function ApiGameDownloadAdded(params = {}) {
  return request('/api/game/downloadAdded', params);
}

/**
 * @function 0.1折游戏列表
 * @param {number} id  151
 * @param {number} page
 * @param {number} listRows
 */
export function ApiGame01DiscountList(params = {}) {
  return request('/cwb/index/getItemSp', params);
}

//========== 公告详情 ============

/**
 * @function 资讯 - 获取单个资讯的详情
 * @param {string} id  资讯id
 */
export function ApiGameNewsRead(params = {}) {
  return request('/api/news/read', params);
}

/**
 * @function 资讯 - 获取相关详情页公告资讯
 * @param {number} game_id  游戏id
 */
export function ApiGameNewstGetTagInformation(params = {}) {
  return request('/api/news/getTagInformation', params);
}

/**
 * @function 资讯 - 资讯收藏
 * @param {number} sourceId  游戏ID
 * @param {string} status  状态-0取消 1收藏
 */
export function ApiGameNewsCollection(params = {}) {
  return request('/api/news/collection', params);
}

/**
 * @function 资讯 - 资讯点赞
 * @param {string} sourceId  资讯id
 * @param {string} status  状态-1取消 1点赞
 */
export function ApiGameNewsLike(params = {}) {
  return request('/api/news/like', params);
}
//========== end ================

/**
 * @function 游戏签到首页
 * @param {string} game_id  测试ID 20040616
 */
export function ApiGameSignInIndex(params = {}) {
  return request('/api/game_sign_in/index', params);
}

/**
 * @function 获取更多签到活动
 * @param {string} page	必填 分页
 * @param {string} listRows 必填 条数
 */
export function ApiGameSignInList(params = {}) {
  return request('/api/game_sign_in/getSignInList', params);
}

/**
 * @function 补签
 * @param {string} game_id  测试ID 20040616
 * @param {string} date  测试ID 必填 日期2023-10-18
 */
export function ApiGameSupplySign(params = {}) {
  return request('/api/game_sign_in/supplySign', params);
}

/**
 * @function 领取连续签到奖励
 * @param {string} id  奖品id 1
 * @param {string} type  类型 0普通奖励 1 vip额外奖励
 */
export function ApiGameTakeSignPrize(params = {}) {
  return request('/api/game_sign_in/getSignInPrize', params);
}
