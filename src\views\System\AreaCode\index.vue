<template>
  <div class="area-code-page">
    <nav-bar-2 :title="$t('选择国家或地区')" :border="true"> </nav-bar-2>
    <div class="main">
      <van-index-bar
        class="code-list"
        :index-list="sortedKeysArray(formatCountryCodes)"
      >
        <div v-for="index in sortedKeysArray(formatCountryCodes)" :key="index">
          <van-index-anchor class="anchor" :index="index" />
          <div
            class="line btn"
            @click="selectCountryCode(country.code)"
            v-for="country in formatCountryCodes[index]"
            :key="country.code"
          >
            <div class="left">{{ country.z_country }}</div>
            <div class="right">+{{ country.code }}</div>
          </div>
        </div>
      </van-index-bar>
    </div>
  </div>
</template>

<script>
import countryCodes from '@/utils/country.data.js';
import { mapMutations } from 'vuex';
export default {
  name: 'AreaCode',
  data() {
    return {
      countryCodes,
    };
  },
  computed: {
    formatCountryCodes() {
      let result = {};
      this.countryCodes.forEach(item => {
        let attr = item.pinyin.substr(0, 1);
        result[attr] ? result[attr].push(item) : (result[attr] = [item]);
      });
      return result;
    },
  },
  methods: {
    ...mapMutations({
      setAreaCode: 'user/setAreaCode',
    }),
    sortedKeysArray(datas) {
      return Object.keys(datas).sort((a, b) => {
        return a > b ? 1 : -1;
      });
    },
    selectCountryCode(code) {
      this.setAreaCode(Number(code));
      this.$nextTick(() => {
        this.back();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.area-code-page {
  background-color: #fff;
  .main {
    padding: 0 * @rem 0 20 * @rem;
    .code-list {
      padding: 0 30 * @rem 0 12 * @rem;
      /deep/ .van-index-anchor {
        color: #fe6600;
        padding: 0;
        height: 40 * @rem;
        line-height: 42 * @rem;
      }
      /deep/ .van-index-anchor--sticky {
        left: 0 !important;
      }
      .line {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40 * @rem;
        border-bottom: 0.5 * @rem solid #ebebeb;
        font-size: 14 * @rem;
      }
    }
  }
}
</style>
