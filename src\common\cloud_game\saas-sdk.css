.HMW_cpPlayerDivClass,
.HMW_rotate-90,
.HMW_rotate90 {
  position: relative;
}
#HMW_playGameBox,
#player_container {
  width: 100%;
  height: 100%;
  position: relative;
}
.HMW_hide {
  display: none;
}
.HMW_rotate90 {
  -moz-transform: rotate(90deg) translateZ(0);
  -webkit-transform: rotate(90deg) translateZ(0);
  -o-transform: rotate(90deg) translateZ(0);
  -ms-transform: rotate(90deg) translateZ(0);
  transform: rotate(90deg) translateZ(0);
  -moz-transform-origin: center center;
  -webkit-transform-origin: center center;
  -o-transform-origin: center center;
  -ms-transform-origin: center center;
  transform-origin: center center;
}
.HMW_rotate-90 {
  -moz-transform: rotate(-90deg) translateZ(0);
  -webkit-transform: rotate(-90deg) translateZ(0);
  -o-transform: rotate(-90deg) translateZ(0);
  -ms-transform: rotate(-90deg) translateZ(0);
  transform: rotate(-90deg) translateZ(0);
  -moz-transform-origin: center center;
  -webkit-transform-origin: center center;
  -o-transform-origin: center center;
  -ms-transform-origin: center center;
  transform-origin: center center;
}
