<template>
  <div id="app" :style="{ '--statusHeight': statusHeight }">
    <router-view></router-view>
  </div>
</template>
<script>
import { mapGetters, mapActions, mapMutations } from 'vuex';
import { platform, authInfo, isAndroidSdk } from '@/utils/box.uni.js';
// 安卓壳顶部状态栏设置
let statusHeight = '0px';
if (['android', 'androidBox'].includes(platform)) {
  // 安卓versionCode:3600以上才隐藏状态栏
  // isAndroidSdk 是安卓原生sdk
  if ((platform == 'andriod' && authInfo.versionCode < 3600) || isAndroidSdk) {
    statusHeight = '0px';
  } else {
    window.BOX.setStatusBarVisibility(false);
    statusHeight =
      window.BOX.getStatusBarHeight() / window.devicePixelRatio + 'px';
  }

  if (isIos) {
    document.body.classList.add('ios');
  } else if (platform == 'android') {
    document.body.classList.add('android');
  }
}
export default {
  data() {
    return {
      statusHeight,
    };
  },
  async created() {
    await this.SET_INIT_DATA();
  },
  methods: {
    ...mapActions({
      SET_INIT_DATA: 'system/SET_INIT_DATA',
    }),
  },
};
</script>
