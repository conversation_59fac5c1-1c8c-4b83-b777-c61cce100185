<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    text: {
      type: String,
      default: '恭喜完成签到任务，您的兑换码是',
    },
    popup_data: {
      type: Object,
      default() {
        return {};
      },
    },
    color: {
      type: String,
      default: '#8965ff',
    },
  },
  computed: {
    popup_show: {
      set(value) {
        this.$emit('changeShow', value);
      },
      get() {
        return this.show;
      },
    },
  },
  methods: {
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.show = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>
<template>
  <div>
    <van-popup
      v-model="popup_show"
      :close-on-click-overlay="true"
      position="center"
      round
      :lock-scroll="false"
      class="exchange-code-popup"
    >
      <div class="title">{{ popup_data.titlegame }}</div>
      <div class="big-text">{{ text }}</div>
      <div class="code">{{ popup_data.cardpass }}</div>
      <div class="small-text">在“我的-我的礼包”查看</div>
      <div
        :style="{ background: color }"
        @click="copy(popup_data.cardpass)"
        class="button"
        >复制</div
      >
    </van-popup>
  </div>
</template>
<style lang="less" scoped>
.exchange-code-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  padding: 30 * @rem 30 * @rem 20 * @rem;
  .title {
    text-align: center;
    font-size: 16 * @rem;
    font-weight: 600;
  }
  .big-text {
    margin: 21 * @rem 0 12 * @rem;
    text-align: center;
    font-size: 14 * @rem;
    color: #777777;
  }
  .small-text {
    margin: 10 * @rem 0 15 * @rem;
    text-align: center;
    font-size: 11 * @rem;
    color: #777777;
  }
  .code {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    font-size: 16 * @rem;
    font-weight: 600;
    height: 70 * @rem;
    border-radius: 8 * @rem;
  }
  .button {
    display: flex;
    width: 100%;
    height: 38 * @rem;
    color: #fff;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    border-radius: 22 * @rem;
  }
}
</style>
