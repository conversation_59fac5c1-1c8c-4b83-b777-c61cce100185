<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    :overlay-style="{ 'z-index': '2997' }"
    class="regression-popup"
  >
    <div
      class="content"
      :style="{ backgroundImage: `url(${regressionData.back_img})` }"
      v-if="regressionData && regressionData.is_regression"
    >
      <div class="container">
        <div class="small-text">{{ regressionData.content }}</div>
        <div class="list">
          <template v-for="(item, index) in regressionData.take_list">
            <template v-if="item.is_list == 0">
              <div class="small-title">{{ item.title }}</div>
              <div class="item">
                <div class="left"></div>
                <div class="center">
                  <div class="big-text">{{ item.lijin }}礼金</div>
                </div>
                <div
                  :class="{ empty: item.status != 1 }"
                  @click="handleGet(item)"
                  class="right"
                >
                  {{ item.status == 2 ? '已领取' : '领取' }}
                </div>
              </div>
            </template>
          </template>
          <div class="small-title">任务奖励</div>
          <template v-for="(item, index) in regressionData.take_list">
            <template v-if="item.is_list == 1">
              <div class="item">
                <div class="left"></div>
                <div class="center">
                  <div class="big-text">{{ item.lijin }}礼金</div>
                  <div class="text">{{ item.title }}</div>
                </div>
                <div
                  :class="{ empty: item.status != 1 }"
                  @click="handleGet(item)"
                  class="right"
                >
                  {{ item.status == 2 ? '已领取' : '领取' }}
                </div>
              </div>
            </template>
          </template>
        </div>
        <div class="bottom-text"
          >有效期倒计时：<span>{{ regressionData.diff_day }}</span
          >天</div
        >
      </div>
    </div>
    <div class="close" @click="setShowRegressionPopup(false)"></div>
  </van-dialog>
</template>
<script>
import { mapMutations, mapGetters, mapActions } from 'vuex';
import { ApiUserTakeRegression } from '@/api/views/users.js';
export default {
  computed: {
    popup: {
      get() {
        return this.showRegressionPopup;
      },
      set(value) {
        this.setShowRegressionPopup(value);
      },
    },
    ...mapGetters({
      showRegressionPopup: 'user/showRegressionPopup',
      regressionData: 'user/regressionData',
    }),
  },
  methods: {
    async handleGet(item) {
      if (item.status != 1) {
        return false;
      }
      const res = await ApiUserTakeRegression({ take_id: item.take_id });
      await this.SET_REGRESSION_POPUP();
      this.$toast(res.msg);
    },
    ...mapMutations({
      setShowRegressionPopup: 'user/setShowRegressionPopup',
    }),
    ...mapActions({
      SET_REGRESSION_POPUP: 'user/SET_REGRESSION_POPUP',
    }),
  },
};
</script>
<style lang="less" scoped>
.regression-popup {
  background-color: rgba(0, 0, 0, 0);
  width: 300 * @rem;
  z-index: 2997 !important;
  .content {
    box-sizing: border-box;
    margin: 55 * @rem auto 0;
    width: 300 * @rem;
    height: 435 * @rem;
    overflow: hidden;
    position: relative;
    padding-top: 152 * @rem;
    background-size: 100%;
    background-repeat: no-repeat;
    .container {
      width: 252 * @rem;
      margin: 0 auto;
      .small-text {
        width: 186px;
        margin: 0 auto 10px;
        font-size: 10px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #c1fbff;
        line-height: 16px;
      }
      .list {
        padding: 0 15 * @rem;
      }
      .small-title {
        margin-top: 6px;
        font-size: 12px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #edd159;
        line-height: 16px;
      }
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40 * @rem;
        .left {
          width: 28 * @rem;
          height: 25 * @rem;
          .image-bg('~@/assets/images/home/<USER>');
        }
        .center {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 6px;
          .big-text {
            font-size: 10px;
            color: #c1fbff;
            line-height: 16px;
          }
          .text {
            font-size: 10px;
            color: #688a8d;
            line-height: 16px;
          }
        }
        .right {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 44px;
          height: 21px;
          background: #ffc254;
          border-radius: 12px;
          font-size: 11px;
          color: #cb2828;
          &.empty {
            background: RGBA(172, 173, 203, 1);
            color: #fff;
          }
        }
      }
    }
    .bottom-text {
      margin-top: 10px;
      margin-left: 3px;
      font-size: 10px;
      color: #c1fbff;
      line-height: 16px;
      span {
        color: rgba(254, 102, 0, 1);
      }
    }
  }

  .close {
    width: 35 * @rem;
    height: 36 * @rem;
    margin: 10 * @rem auto 0;
    background: url(~@/assets/images/close.png) center center no-repeat;
    background-size: 26 * @rem 26 * @rem;
  }
}
</style>
