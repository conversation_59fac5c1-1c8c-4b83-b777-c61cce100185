<template>
  <div class="down-container">
    <div class="left-box">
      <div class="logo">
        <img src="~@/assets/images/app-icon2.png" alt="" />
      </div>
      <div class="content">
        <div class="title">3733游戏盒</div>
        <div class="message">免费送0元首充 | 充值0.1折起</div>
      </div>
    </div>
    <div class="right-box">
      <div class="btn" @click="handleDownload()">立即下载</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'name',
  methods: {
    handleDownload() {
      window.location.href = 'https://app.3733.com/';
    },
  },
};
</script>

<style lang="less" scoped>
.down-container {
  position: fixed;
  box-sizing: border-box;
  z-index: 9;
  left: 0;
  top: 0;
  width: 100%;
  height: 76 * @rem;
  background: #33312d;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18 * @rem;
  .left-box {
    display: flex;
    align-items: center;
    .logo {
      width: 50 * @rem;
      height: 50 * @rem;
    }
    .content {
      margin-left: 8 * @rem;
      display: flex;
      flex-direction: column;
      .title {
        height: 19 * @rem;
        font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
        font-weight: bold;
        font-size: 19 * @rem;
        color: #fffcec;
        line-height: 19 * @rem;
      }
      .message {
        margin-top: 11 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #ffffff;
      }
    }
  }
  .right-box {
    .btn {
      width: 84 * @rem;
      height: 30 * @rem;
      background: linear-gradient(90deg, #fcf7d1 0%, #f7d7b0 100%);
      border-radius: 23 * @rem;
      font-weight: 600;
      font-size: 15 * @rem;
      color: #000000;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
    }
  }
}
</style>
