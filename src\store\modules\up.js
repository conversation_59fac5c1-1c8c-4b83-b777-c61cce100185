export default {
  state: {
    selectedGameList: [],
  },
  getters: {
    selectedGameList(state) {
      return state.selectedGameList;
    },
  },
  mutations: {
    setSelectedGameList(state, payload) {
      console.log(payload);
      if (!payload) {
        // 没有payload就是删除
        state.selectedGameList = [];
      } else if (payload.length) {
        // payload为数组就是赋值
        state.selectedGameList = payload;
      } else if (typeof payload == 'number') {
        // payload为数字就是删除
        state.selectedGameList = state.selectedGameList.filter(item => {
          return item.id != payload;
        });
      } else {
        if (!state.selectedGameList.some(item => item.id == payload.id)) {
          state.selectedGameList.push(payload);
        }
      }
    },
  },
};
