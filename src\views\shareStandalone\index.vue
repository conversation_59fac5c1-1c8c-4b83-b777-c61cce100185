<template>
  <div class="share-page">
    <div class="content1">
      <img src="~@/assets/images/share-standalone/pic1.png" alt="" />
      <div class="invite">已有{{ invite }}人接受邀请</div>
      <div class="user-info" v-if="user">
        <div class="user-avatar">
          <img
            :src="user.avatar != '' ? user.avatar : 'img/img_user_default.png'"
            alt=""
          />
        </div>
        <div class="info">
          <div class="name">{{ user.nickname }}：我正在3733玩游戏</div>
          <div class="desc">游戏充值只要0.1折</div>
        </div>
      </div>
      <div class="download-btn" @click="download()">
        <img src="~@/assets/images/share-standalone/main-btn.png" alt="" />
      </div>
    </div>
    <div class="tips-box" v-if="!isIOS">
      <div class="tips">
        <div class="text">
          复制口令，进入3733游戏盒进行注册登录即可完成助力
          <span @click="copy('口令复制成功，快去注册登录3733游戏盒吧')"
            >点击复制</span
          >
        </div>
      </div>
    </div>
    <div class="content2">
      <img src="~@/assets/images/share-standalone/pic2.png" alt="" />

      <swiper
        class="swiper-container"
        :options="swiperOptions"
        :auto-update="true"
        v-if="swiperShow"
      >
        <swiper-slide class="select-item">
          <img src="~@/assets/images/share-standalone/swiper-img2.png" alt="" />
        </swiper-slide>
        <swiper-slide class="select-item">
          <img src="~@/assets/images/share-standalone/swiper-img3.png" alt="" />
        </swiper-slide>
        <swiper-slide class="select-item">
          <img src="~@/assets/images/share-standalone/swiper-img1.png" alt="" />
        </swiper-slide>
      </swiper>
    </div>
    <img src="~@/assets/images/share-standalone/pic3.png" alt="" />
    <img
      src="~@/assets/images/share-standalone/pic4.png"
      alt=""
      v-if="!isIOS"
    />
    <img src="~@/assets/images/share-standalone/pic4-2.png" alt="" v-else />
    <div class="content3">
      <img src="~@/assets/images/share-standalone/pic5.png" alt="" />
      <div class="download-btn" @click="download(false)"></div>
    </div>
    <div class="wxqq-popup" v-if="wxpopupShow" @click="hideWxpopup()">
      <img src="~@/assets/images/wxqq.png" alt="" />
    </div>
  </div>
</template>

<script>
import { ApiUserInviteInfo } from '@/api/views/users';
export default {
  name: 'Share',
  data() {
    return {
      isIOS: false,
      isWX: false,
      wxpopupShow: false,
      userid: '',
      channel: '',
      invite: 0,
      swiperShow: false,
      user: {
        avatar: '',
        nickname: '',
      },
      swiperOptions: {
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        observeParents: true,
        effect: 'coverflow',
        slidesPerView: 'auto',
        centeredSlides: true,
        loop: true,
        coverflowEffect: {
          rotate: 50,
          stretch: 0,
          depth: 50,
          modifier: 1,
          slideShadows: false,
        },
      },
      inviteKey: '',
      azurl:
        'https://kkxz.xz3733.com/apk/gamebox/latest/3733gamebox_cps172.apk',
      iosurl: 'https://game.3733.com',
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const ua = navigator.userAgent.toLowerCase();
      this.isIOS = /iphone|ipad|ipod|ios|Macintosh/i.test(ua);
      this.isWX = /micromessenger\/[0-9]/i.test(ua) || /qq\/[0-9]/i.test(ua);
      const urlParams = new URLSearchParams(window.location.search);
      this.userid = urlParams.get('user_id');
      this.channel = urlParams.get('channel');
      let res = await ApiUserInviteInfo({
        userId: this.userid,
        channel: this.channel,
      });
      this.user = res.data.user_info;
      this.azurl = res.data.down_url;
      this.iosurl = res.data.web_url;
      this.inviteKey = res.data.invite_key;
      this.invite = res.data.invite_count;
      this.swiperShow = true;
    },
    download(isCopy = true) {
      if (this.isWX) {
        this.wxpopupShow = true;
        return;
      }
      if (isCopy && !this.isIOS) {
        this.copy();
      }
      if (this.isIOS) {
        window.location.href = this.iosurl;
      } else {
        window.location.href = this.azurl;
      }
    },
    copy(toastText = '口令已复制成功') {
      this.$copyText(this.inviteKey).then(
        res => {
          this.$toast(toastText);
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
          });
        },
      );
    },
    hideWxpopup() {
      this.wxpopupShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.share-page {
  img {
    width: 100%;
  }

  .content1 {
    position: relative;

    .invite {
      display: block;
      width: 100%;
      text-align: center;
      height: 21 * @rem;
      font-size: 17 * @rem;
      color: #fff;
      line-height: 21 * @rem;
      font-weight: bold;
      //  color: #fffce1;
      // -webkit-text-stroke: 1 * @rem rgba(212, 51, 0, 0.83);
      // text-stroke: 1 * @rem rgba(212, 51, 0, 0.83);
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: #fffce1;
      position: absolute;
      top: 144 * @rem;
      left: 0;
    }

    .user-info {
      display: flex;
      align-items: center;
      width: 291 * @rem;
      position: absolute;
      top: 213 * @rem;
      left: 42 * @rem;

      .user-avatar {
        display: block;
        width: 40 * @rem;
        height: 40 * @rem;
        margin-right: 12 * @rem;

        img {
          display: block;
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .info {
        flex: 1;
        min-width: 0;

        .name {
          display: block;
          height: 19 * @rem;
          font-size: 14 * @rem;
          line-height: 19 * @rem;
          letter-spacing: -0.3 * @rem;
          font-weight: bold;
          color: #121212;
          overflow: hidden;
        }

        .desc {
          display: block;
          height: 13 * @rem;
          font-size: 13 * @rem;
          color: #121212;
          line-height: 13 * @rem;
          overflow: hidden;
          margin-top: 5 * @rem;
        }
      }
    }

    .download-btn {
      display: block;
      width: 303 * @rem;
      height: 43 * @rem;
      position: absolute;
      top: 576 * @rem;
      left: 36 * @rem;
      cursor: pointer;

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
  }

  .tips-box {
    display: block;
    width: 100%;
    padding: 12 * @rem 0;
    background: url(~@/assets/images/share-standalone/bg.png) no-repeat;
    background-size: 100%;

    .tips {
      width: 303 * @rem;
      height: 38 * @rem;
      margin: 0 auto;
      overflow: hidden;
      background: linear-gradient(
        92deg,
        rgba(255, 255, 255, 0) 1%,
        rgba(255, 255, 255, 0.8) 17%,
        rgba(255, 255, 255, 0.8) 83%,
        rgba(255, 255, 255, 0) 100%
      );

      .text {
        width: 226 * @rem;
        height: 30 * @rem;
        font-size: 12 * @rem;
        font-weight: bold;
        color: #121212;
        line-height: 15 * @rem;
        text-align: center;
        margin: 4 * @rem auto;

        span {
          line-height: 15 * @rem;
          font-size: 12 * @rem;
          font-weight: bold;
          text-decoration: underline;
          color: #ff1a51;
          cursor: pointer;
        }
      }
    }
  }

  .content2 {
    position: relative;

    .swiper-container {
      height: 400 * @rem;
      width: 100%;
      position: absolute;
      top: 91 * @rem;
      left: 0;

      .select-item {
        width: 225 * @rem;
      }
    }
  }

  .content3 {
    position: relative;

    .download-btn {
      display: block;
      width: 303 * @rem;
      height: 47 * @rem;
      position: absolute;
      top: 15 * @rem;
      left: 36 * @rem;
      cursor: pointer;
    }
  }

  .wxqq-popup {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;

    img {
      width: 80%;
      height: auto;
      position: absolute;
      top: 0;
      right: 10 * @rem;
    }
  }
}
</style>
