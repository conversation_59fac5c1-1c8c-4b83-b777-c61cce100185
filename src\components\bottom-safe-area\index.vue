<template>
  <div
    class="bottom-safe-area-component"
    :style="{ backgroundColor: `${bgColor}` }"
  ></div>
</template>

<script>
export default {
  name: 'BottomSafeArea',
  props: {
    bgColor: {
      type: String,
      default: 'transparent',
    },
  },
};
</script>

<style lang="less" scoped>
.bottom-safe-area-component {
  width: 100%;
  height: @safeAreaBottom;
  height: @safeAreaBottomEnv;
  flex-shrink: 0;
}
</style>
