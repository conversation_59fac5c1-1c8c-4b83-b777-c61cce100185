import Vue from 'vue';
import VueRouter from 'vue-router';

// 重写push和replace,拦截跳转同一路由时报错
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};
const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch(err => err);
};

Vue.use(VueRouter);

// require.context() webpack的方法，文件工程化导入
let routerList = [];

function importAll(r) {
  r.keys().forEach(fileName => {
    routerList.push(...r(fileName).default);
  });
}
importAll(require.context('./', true, /\.router\.js/));

const routes = [
  {
    path: '/check_pay_result',
    name: 'CheckPayResult',
    component: () =>
      import(
        /* webpackChunkName: "check_pay_result" */ '@/views/CheckPayResult'
      ),
    meta: {
      keepAlive: false,
    },
  },
  ...routerList,
];

const router = new VueRouter({
  // history模式好像不太行
  // 记录各个滚动条位置
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return {
        x: 0,
        y: 0,
      };
    }
  },
  routes,
});

export default router;
