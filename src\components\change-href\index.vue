<template>
  <div class="change-href" v-if="show">
    <div class="open-btn btn" @click="showPopup">内部参数</div>
    <van-popup class="popup-container" v-model="popup" :lock-scroll="false">
      <div class="title">自定义url</div>
      <div class="operate">
        <input type="text" v-model="url" />
        <div class="operate-btn confirm" @click="confirm">确定</div>
      </div>
      <div class="list">
        <div class="item" v-for="item in urlList" :key="item">
          {{ item }} <span class="btn" @click="url = item">选择</span>
        </div>
      </div>
      <div class="device-info list" v-if="authInfo">
        <div class="title">App端系统参数</div>
        <div class="item" v-for="(item, index) in authInfo" :key="index">
          {{ index }}: {{ String(item).trim() }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { authInfo, packageName, from } from '@/utils/box.uni.js';
let url = 'https://game.3733.com';
export default {
  name: 'changeHref',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      popup: false,
      url: 'https://aagame.3733.com',
      urlList: [
        'https://game.3733.com',
        'https://aagame.3733.com',
        'https://ccgame.3733.com',
      ],
    };
  },
  computed: {
    authInfo() {
      if (!authInfo) {
        return false;
      }
      return {
        from,
        packageName,
        ...authInfo,
      };
    },
  },
  created() {
    this.url = window.location.href;
  },
  methods: {
    showPopup() {
      this.popup = true;
    },
    confirm() {
      this.popup = false;
      this.$nextTick(() => {
        window.location.href = this.url;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.change-href {
  width: 355 * @rem;
  height: 40 * @rem;
  background-color: #fff;
  border-radius: 6 * @rem;
  margin: 10 * @rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16 * @rem;
  overflow: hidden;
  .open-btn {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    color: #333;
  }
  .popup-container {
    box-sizing: border-box;
    padding: 20 * @rem 18 * @rem;
    width: 300 * @rem;
    height: auto;
    border-radius: 12 * @rem;
    background-color: #fff;
    .title {
      text-align: center;
      line-height: 40 * @rem;
      font-size: 18 * @rem;
      font-weight: bold;
      color: #000;
    }

    .operate {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10 * @rem auto 0;
      input {
        padding: 0 10 * @rem;
        border: 1 * @rem solid #ebebeb;
        display: block;
        flex: 1;
        min-width: 0;
        height: 40 * @rem;
        line-height: 40 * @rem;
        border-radius: 8 * @rem;
        font-size: 13 * @rem;
        color: #000;
      }
      .operate-btn {
        width: 60 * @rem;
        height: 40 * @rem;
        border-radius: 8 * @rem;
        background-color: @themeColor;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 14 * @rem;
        margin-left: 10 * @rem;
        &.reset {
          background-color: rgb(66, 137, 134);
        }
      }
    }
    .list {
      margin: 20 * @rem 10 * @rem 0;
      .item {
        display: flex;
        height: 30 * @rem;
        align-items: center;
        font-size: 14 * @rem;
        span {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 40 * @rem;
          height: 20 * @rem;
          border-radius: 6 * @rem;
          color: @themeColor;
          margin-left: 10 * @rem;
          font-size: 12 * @rem;
          background-color: @themeColor;
          color: #fff;
        }
      }
    }
    .device-info {
      border: 1px solid #ccc;
      border-radius: 12 * @rem;
      padding: 10 * @rem 14 * @rem;
      .item {
        line-height: 20 * @rem;
        height: unset;
        font-size: 12 * @rem;
      }
    }
  }
}
</style>
