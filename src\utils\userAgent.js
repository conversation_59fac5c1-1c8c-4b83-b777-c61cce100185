const ua = navigator.userAgent.toLowerCase();
// ios12出现了navigator.maxTouchPoints的问题, maxTouchPoints不设置的话mac电脑触控为0会被判定为ios
// export const isIos = /iphone|ipad|ipod|ios|Macintosh/i.test(ua) && navigator.maxTouchPoints>0;
export const isIos = /iphone|ipad|ipod|ios|Macintosh/i.test(ua);
export const isAndroid = /android/i.test(ua); // 安卓内嵌页面(包含android马甲包)
export const isAndroidBox = /yyazwebview/i.test(ua); // 安卓马甲包
export const isIosBox = /yyioswebview/i.test(ua) || /yyioswebview/i.test(ua); // ios马甲包
export const isWechat = ua.match(/MicroMessenger\/[0-9]/i);
export const isWebApp = window.navigator.standalone;
// 在iosbox内有些壳会不是safari
export const isSafari =
  ua.indexOf('applewebkit') > -1 &&
  ua.indexOf('mobile') > -1 &&
  ua.indexOf('safari') > -1 &&
  ua.indexOf('browser') === -1 &&
  ua.indexOf('linux') === -1 &&
  ua.indexOf('android') === -1 &&
  ua.indexOf('chrome') === -1 &&
  ua.indexOf('ios') === -1;
// webapp、iosbox不打开
export const needGuide =
  process.env.NODE_ENV !== 'development'
    ? !isWebApp && !isIosBox && !isAndroidBox
    : false;
// 返回IOS当前系统版本，如：15，只会在真机上显示，模拟器无效
export const iosSystem = window.navigator.userAgent
  .toLowerCase()
  .match(/cpu iphone os (\d+)\_(\d+) like/)
  ? window.navigator.userAgent
      .toLowerCase()
      .match(/cpu iphone os (\d+)\_(\d+) like/)[1]
      .replace(/_/g, '.')
  : 0;
