import { request } from '../index';

// 限时兑换列表
export function ApigoldExchangeList(params = {}) {
  return request('/api/gold_exchange/exchangeList', params);
}

// 兑换
/**
 * @param ex_info_id 兑换列表中info列表的id
 * */
export function ApigoldExchangeRaffle(params = {}) {
  return request('/api/gold_exchange/raffle', params);
}

/**
 * 金币兑换平台币
 */
// 金币兑换平台币 - 兑换
export function ApigoldToPtbExchange(params = {}) {
  return request('/api/gold_to_ptb/exchange', params);
}
// 金币兑换平台币 - 兑换列表
export function ApigoldToPtbExchangeList(params = {}) {
  return request('/api/gold_to_ptb/exchangeList', params);
}
// 金币兑换平台币 - 兑换详情
export function ApigoldToPtbExchangeInfo(params = {}) {
  return request('/api/gold_to_ptb/exchangeInfo', params);
}
// 金币兑换平台币 - 检查兑换条件
export function ApigoldToPtbCheckExchange(params = {}) {
  return request('/api/gold_to_ptb/checkExchange', params);
}
