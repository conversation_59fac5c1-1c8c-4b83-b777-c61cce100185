import { request } from '../index';

// 礼包列表
export function ApiCardIndex(params = {}) {
  return request('/api/card/index', params);
}

// 领取礼包
export function ApiCardGet(params = {}) {
  return request('/api/card/get', params);
}

// 礼包详情
export function ApiCardRead(params = {}) {
  return request('/api/card/read', params);
}

// 648福利列表
export function ApiCardGet648CardList(params = {}) {
  return request('/api/card/get648CardList', params);
}

// 648福利已领取列表
export function ApiCardGetMy648CardList(params = {}) {
  return request('/api/card/getMy648CardList', params);
}

// 648福利已领取用户列表
export function ApiCardGetMemGet648CardLog(params = {}) {
  return request('/api/card/getMemGet648CardLog', params);
}
