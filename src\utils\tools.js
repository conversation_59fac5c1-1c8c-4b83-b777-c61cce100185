/**
 * 汇率转换
 * @param { number } amount 金额
 * @param { number } rate 汇率
 * @returns { number } 转换后的金额
 */
export function formatExchangeRate(amount, rate) {
  if (!rate) {
    return amount;
  }
  amount = amount * rate;
  const amount_1 = Number(amount.toFixed(2));
  const amount_2 = Number(amount.toFixed(3));
  if (amount_1 < amount_2) {
    return Number((Number(amount_1) + 0.01).toFixed(2));
  }
  return amount_1;
}
