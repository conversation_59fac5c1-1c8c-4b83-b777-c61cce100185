<template>
  <div
    class="rubber-band"
    @touchstart.stop="touchstart"
    @touchmove.stop="touchmove"
  >
    <slot />
  </div>
</template>
<script>
import { isIosBox, isWebApp } from '@/utils/userAgent';
export default {
  names: 'rubberBand',
  props: {
    topColor: {
      type: String,
      default: 'white',
    },
    bottomColor: {
      type: String,
      default: 'white',
    },
  },
  data() {
    return {
      touchStartPointY: '',
      distance: '',
    };
  },
  activated() {
    if (isIosBox || isWebApp) {
      document
        .querySelector('body')
        .setAttribute('style', 'background:' + this.topColor);
    }
  },
  watch: {
    topColor(val) {
      if (isIosBox || isWebApp) {
        document
          .querySelector('body')
          .setAttribute('style', 'background:' + val);
      }
    },
    bottomColor(val) {
      if (isIosBox || isWebApp) {
        document
          .querySelector('body')
          .setAttribute('style', 'background:' + val);
      }
    },
  },
  methods: {
    touchstart() {
      if (isIosBox || isWebApp) {
        let touch;
        if (event.touches) {
          touch = event.touches[0]; //获取当前事件
        } else {
          touch = event;
        }
        this.touchStartPointY = touch.clientY;
      }
    },
    touchmove() {
      if (isIosBox || isWebApp) {
        let touch;
        if (event.touches) {
          touch = event.touches[0];
        } else {
          touch = event;
        }
        this.distance = touch.clientY - this.touchStartPointY;

        if (this.distance > 0) {
          document
            .querySelector('body')
            .setAttribute('style', 'background:' + this.topColor);
        }
        if (this.distance < 0) {
          document
            .querySelector('body')
            .setAttribute('style', 'background:' + this.bottomColor);
        }
      }
    },
  },
};
</script>
<style lang="less" scoped></style>
