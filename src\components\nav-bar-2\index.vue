<template>
  <div class="nav-bar-component" ref="navBar" v-show="navBarShow">
    <div class="bg" :class="bgStyle">
      <van-nav-bar
        fixed
        :placeholder="placeholder"
        safe-area-inset-top
        :border="border"
        class="nav-bar"
        :style="styleObject"
      >
        <template #left>
          <slot name="left">
            <div class="back" @click="back" v-if="backShow"></div>
          </slot>
        </template>
        <template #title>
          <slot name="center">
            <div class="nav-title">{{ title }}</div>
          </slot>
        </template>
        <template #right>
          <slot name="right"></slot>
        </template>
      </van-nav-bar>
    </div>
  </div>
</template>

<script>
import { platform, isSdk } from '@/utils/box.uni.js';
import { NavBar } from 'vant';
import 'vant/lib/nav-bar/style/less';
export default {
  name: 'NavBar',
  components: {
    'van-nav-bar': NavBar,
  },
  props: {
    backShow: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '',
    },
    bgStyle: {
      type: String,
      validator: function (value) {
        return ['white', 'transparent', 'transparent-white', 'black'].includes(
          value,
        );
      },
      default: 'white',
    },
    bgColor: {
      //只针对背景为透明时增加背景色
      type: String,
      default: 'rgba(255, 255, 255, 0)',
    },
    border: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: Boolean,
      default: true,
    },
    azShow: {
      type: Boolean,
      default: false,
    },
    sdkShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isSdk,
      platform,
      clientHeight: 22,
    };
  },
  computed: {
    styleObject() {
      let type = ['transparent', 'transparent-white'];
      if (this.bgColor && type.includes(this.bgStyle)) {
        return {
          background: `${this.bgColor}`,
        };
      } else {
        return {};
      }
    },
    navBarShow() {
      if (this.isSdk) {
        return this.sdkShow ? true : false;
      }
      if (this.platform == 'android') {
        return this.azShow ? true : false;
      } else {
        return true;
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.clientHeight = this.$refs.navBar.clientHeight;
    });
  },
};
</script>
<style lang="less" scoped>
.nav-bar-component {
  flex-shrink: 0;
  /deep/ .van-nav-bar--fixed {
    .fixed-center;
  }
  .white {
    /deep/ .van-nav-bar {
      background: #fff;
      .back {
        width: 30 * @rem;
        height: 50 * @rem;
        background: url(../../assets/images/nav-bar-back-black.png) center
          center no-repeat;
        background-size: 10 * @rem 18 * @rem;
      }
      .nav-title {
        font-size: 18 * @rem;
        color: #000;
        font-weight: 400;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        height: 50 * @rem;
        line-height: 50 * @rem;
      }
    }
  }
  .black {
    /deep/ .van-nav-bar {
      background: #1d1b23;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/back-light.png) center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #f4dfc6;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
  .transparent {
    /deep/ .van-nav-bar {
      background: transparent;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/nav-bar-back-black.png) center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #000;
      font-weight: 400;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
  .transparent-white {
    /deep/ .van-nav-bar {
      background: transparent;
    }
    .back {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(../../assets/images/nav-bar-back-white.png) center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
    .nav-title {
      font-size: 18 * @rem;
      color: #fff;
      font-weight: 400;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 50 * @rem;
      line-height: 50 * @rem;
    }
  }
}
</style>
