import { request } from '../index';

/**
 * 赏金任务 - 我的任务
 */
export function ApiBountyTaskGetMyTask(params = {}) {
  return request('/api/bounty_task/getMyTask', params);
}

/**
 * 赏金任务 - 赏金任务首页
 */
export function ApiBountyTaskIndex(params = {}) {
  return request('/api/bounty_task/index', params);
}

/**
 * 赏金任务 - 赏金任务规则
 */
export function ApiBountyTaskGetRuleInfo(params = {}) {
  return request('/api/bounty_task/getRuleInfo', params);
}

/**
 * 赏金任务 - 赏金猎人
 */
export function ApiBountyTaskGetBountyHunter(params = {}) {
  return request('/api/bounty_task/getBountyHunter', params);
}

/**
 * 赏金任务 - 领取任务
 */
export function ApiBountyTaskGetTask(params = {}) {
  return request('/api/bounty_task/getTask', params);
}

/**
 * 赏金任务 - 领取任务奖励
 */
export function ApiBountyTaskGetMyTaskReward(params = {}) {
  return request('/api/bounty_task/getTaskReward', params);
}
