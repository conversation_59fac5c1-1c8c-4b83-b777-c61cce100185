<template>
  <div class="search-container" :class="{ white: white }">
    <div class="search-bar" @click="$router.push('/search')">
      <div class="icon"></div>
      <div class="search-text">{{ $t('搜你想玩的游戏') }}</div>
    </div>
    <div class="grq" @click="toDownManagement"></div>
    <div @click="toPage('Notice')" class="message">
      <div class="dot" v-if="unreadCount.sum > 0">
        {{ unreadCount.sum > 99 ? '99+' : unreadCount.sum }}
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { BOX_showActivity } from '@/utils/box.uni.js';
import { ApiGameCheckDown } from '@/api/views/game.js';

export default {
  props: {
    white: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    ...mapGetters({
      unreadCount: 'system/unreadCount',
    }),
  },
  methods: {
    async toDownManagement() {
      const checkRes = await this.checkDownload();
      if (!checkRes) {
        return false;
      }
      BOX_showActivity(
        { name: 'GrqList', isAndroidBoxToNative: true },
        { page: 'yygl' },
      );
    },
    // 下载按钮防沉迷check
    async checkDownload() {
      const res = await ApiGameCheckDown();
      if (res.code > 0) {
        return true;
      }
      return false;
    },
  },
};
</script>
<style lang="less" scoped>
.search-bar {
  flex: 1;
  min-width: 0;
  box-sizing: border-box;
  padding: 0 19 * @rem;
  width: 310 * @rem;
  height: 35 * @rem;
  border-radius: 18 * @rem;
  background-color: #f5f5f6;
  display: flex;
  align-items: center;
  .icon {
    width: 14 * @rem;
    height: 14 * @rem;
    background: url(~@/assets/images/home/<USER>
      no-repeat;
    background-size: 14 * @rem 14 * @rem;
  }
  .search-text {
    font-size: 14 * @rem;
    color: #9a9a9a;
    margin-left: 7 * @rem;
  }
}
.search-container {
  display: flex;
  align-items: center;
  padding: 10 * @rem 18 * @rem;
  .grq {
    width: 17 * @rem;
    height: 18 * @rem;
    padding: 5 * @rem 5 * @rem 5 * @rem 15 * @rem;
    background-image: url(~@/assets/images/download-black.png);
    background-size: 17 * @rem;
    background-position: 16 * @rem 5 * @rem;
    background-repeat: no-repeat;
  }
  .message {
    padding: 5 * @rem;
    width: 17 * @rem;
    height: 18 * @rem;
    margin-left: 10 * @rem;
    background-size: 17 * @rem 18 * @rem;
    background-repeat: no-repeat;
    background-image: url(~@/assets/images/notice.png);
    background-position: center center;
    position: relative;
    .dot {
      position: absolute;
      left: 50%;
      top: -2 * @rem;
      padding: 0 5 * @rem;
      height: 14 * @rem;
      border-radius: 7 * @rem;
      background-color: #fe4a55;
      color: #fff;
      font-size: 10 * @rem;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  &.white {
    .search-bar {
      background-color: rgba(245, 245, 246, 0.1);
      .icon {
        background-image: url(~@/assets/images/home/<USER>
      }
      .search-text {
        color: #fff;
      }
    }
    .grq {
      background-image: url(~@/assets/images/download-white.png);
    }
    .message {
      background-image: url(~@/assets/images/notice-white.png);
    }
  }
}
</style>
