<template>
  <div class="black-wapper" v-if="isShow && showSuspension == 1">
    <div class="wechat-tips" v-if="isWechat">
      <img src="@/assets/images/tips/wechat-tips.png" />
    </div>
    <template v-else>
      <div class="tips" v-if="isIos">
        <van-popup
          v-if="isSafari"
          v-model="show"
          :lock-scroll="iosSystem == 15 ? false : true"
          position="bottom"
          round
          @closed="openButton()"
          class="safari-popup tips-popup"
        >
          <!-- 新 -->
          <div class="container">
            <img src="@/assets/images/tips/no-safari-tips.png" alt="" />
          </div>
          <!-- 旧 -->
          <!-- <div class="container">
            <div class="title">
              <div class="text">
                {{ $t("添加") }}<span>{{ appName }}</span
                >{{ $t("到桌面") }}<i>（{{ $t("免安装") }}）</i>
              </div>
              <div class="small-text">
                <span class="color"
                  >全网BT手游充值0.1折起、免费0元首充、送648代金券享折上折</span
                >
              </div>
              <div class="img-list">
                <div class="img-item img1"></div>
                <div class="img-item img2"></div>
                <div class="img-item img3"></div>
              </div>
            </div>
            <div class="img-bg"></div>
            <div v-if="isSafari" class="down-arrow"></div>
          </div> -->
        </van-popup>
        <van-popup
          v-if="!isSafari"
          v-model="noSafariShow"
          position="bottom"
          round
          @closed="openButton()"
          class="no-safari-popup tips-popup"
        >
          <div class="title">
            {{ $t('添加桌面小贴士')
            }}<span @click="noSafariShow = false" class="close"></span>
          </div>
          <div class="content">
            <div class="item">
              <div class="big-text">
                <span class="sign">1.</span
                ><span class="text"
                  >{{ $t('使用Safari浏览器访问') }}{{ appName }}</span
                >
              </div>
              <div class="small-text">
                {{ $t('请复制链接后，在') }}<i class="icon"></i
                >{{ $t('“Safari”浏览器打开') }}。<span
                  @click="copy()"
                  class="text_style color blue"
                  >{{ $t('复制') }}</span
                >
              </div>
            </div>
            <div class="item">
              <div class="big-text">
                <span class="sign">2.</span
                ><span class="text">{{
                  $t('按照Safari添加桌面提示操作')
                }}</span>
              </div>
              <div class="small-text">
                {{ $t('点击Safari下方工具栏上的')
                }}<i class="icon icon1"></i>，{{ $t('并选择')
                }}<i class="icon icon2"></i>“<span class="color red">{{
                  $t('添加到主屏幕')
                }}</span
                >”
              </div>
            </div>
            <div class="item">
              <div class="big-text">
                <span class="sign">3.</span
                ><span class="text"
                  >{{ $t('返回手机桌面，点击') }}【{{ appName }}】{{
                    $t('即可进入')
                  }}</span
                >
              </div>
              <div class="small-text">
                {{ $t('使用') }}【{{ appName }}{{ $t('桌面版') }}】{{
                  $t(
                    '永不掉签，签到、转盘、帐号交易功能更方便，还能免费领取3733金币、648充值抵用券及首充、福利礼包',
                  )
                }}
              </div>
            </div>
          </div>
        </van-popup>
        <div v-if="buttonShow" @click="openPopup()" class="fix-button"></div>
      </div>
      <div class="tips" v-if="isAndroid && !isAndroidBox">
        <div @click="downloadBox()" class="fix-button"></div>
      </div>
    </template>
  </div>
</template>

<script>
import {
  isWechat,
  isSafari,
  needGuide,
  isIos,
  isAndroid,
  isAndroidBox,
  iosSystem,
} from '@/utils/userAgent';
import BASEPARAMS from '@/utils/baseParams';
import { platform } from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      isShow: false, //是否展示引导
      href: '', //当前地址
      show: true, //是否展示safari软引导层
      noSafariShow: false, //是否展示非safari软引导层
      buttonShow: false, //是否打开按钮
      isWechat,
      isSafari,
      isIos,
      isAndroid,
      isAndroidBox,
      iosSystem,
    };
  },
  computed: {
    ...mapGetters({
      showSuspension: 'system/showSuspension',
    }),
  },
  created() {
    switch (platform) {
      case 'ios':
      case 'iosBox':
      case 'android':
      case 'androidBox':
        this.isShow = false;
        break;
      default:
        // 判断是否打开引导
        this.isShow = needGuide;
        break;
    }

    // 判断是打开safari引导还是非safari引导
    if (this.isSafari) {
      this.show = true;
      this.noSafariShow = false;
    } else {
      this.show = false;
      this.noSafariShow = true;
    }

    // 如果是微信引导防手势和超出隐藏
    if (this.isShow && this.isWechat) {
      document.getElementsByTagName('body')[0].style.overflow = 'hidden';
      document.addEventListener('gesturestart', function (event) {
        event.preventDefault();
      });
    }
  },
  methods: {
    openButton() {
      this.buttonShow = true;
    },
    openPopup() {
      // 打开引导
      if (this.isSafari) {
        this.show = true;
      } else {
        this.noSafariShow = true;
      }
      this.buttonShow = false;
    },
    copy() {
      event.stopPropagation();
      this.href = window.location.href;
      this.$copyText(this.href).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    downloadBox() {
      const url = !!BASEPARAMS.channel
        ? `https://xz.xz3733.com/apk/gamebox/latest/3733gamebox_${BASEPARAMS.channel}.apk`
        : `https://xz.xz3733.com/apk/gamebox/latest/3733gamebox_cps3457.apk`;
      window.location.href = url;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .tips .van-overlay {
  z-index: 10000 !important;
}

/deep/ .tips-popup {
  z-index: 99999 !important;
}
.wechat-tips {
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10001;
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 0.388933rem;
}

.wechat-tips img {
  position: absolute;
  top: 0;
  right: 0;
  width: 80%;
  height: auto;
}
.fix-button {
  position: fixed;
  bottom: calc(70 * @rem + @safeAreaBottom);
  bottom: calc(70 * @rem + @safeAreaBottomEnv);
  transform: translate(290 * @rem, 0);
  z-index: 9999999;
  width: 70 * @rem;
  height: 67 * @rem;
  background-image: url(~@/assets/images/tips/guide-ball.gif);
  background-repeat: no-repeat;
  background-size: 100%;
}

.safari-popup {
  .container {
    position: relative;
    .title {
      box-sizing: border-box;
      padding: 15 * @rem;
      .text {
        font-size: 20 * @rem;
        font-weight: bold;
        span {
          color: #ff4a3d;
        }
        i {
          font-style: normal;
          font-size: 14 * @rem;
          color: #ff4a3d;
        }
      }
      .small-text {
        margin-top: 10 * @rem;
        span {
          color: #999;
          &.color {
            color: #f60;
          }
        }
      }
      .img-list {
        display: flex;
        margin: 15 * @rem 0 0;
        .img-item {
          width: 130 * @rem;
          height: 119 * @rem;
          background-size: 100%;
          background-repeat: no-repeat;
          &.img1 {
            background-image: url(~@/assets/images/tips/safari-img1.png);
          }
          &.img2 {
            background-image: url(~@/assets/images/tips/safari-img2.png);
          }
          &.img3 {
            background-image: url(~@/assets/images/tips/safari-img3.png);
          }
        }
      }
    }
    .img-bg {
      display: block;
      width: 345 * @rem;
      height: 170 * @rem;
      margin: 0 auto;
      background-image: url(~@/assets/images/tips/safari-img4.png);
      background-repeat: no-repeat;
      background-size: 100%;
    }
    .down-arrow {
      margin: 10 * @rem auto 20 * @rem;
      width: 25 * @rem;
      height: 22.5 * @rem;
      background-image: url(~@/assets/images/tips/safari-img5.gif);
      background-size: 100%;
      background-repeat: no-repeat;
      -webkit-animation: downward 0.8s ease-in-out infinite;
      animation: downward 0.8s ease-in-out infinite;
    }
  }
}

.no-safari-popup {
  .title {
    height: 44 * @rem;
    text-align: center;
    line-height: 44 * @rem;
    font-size: 16 * @rem;
    border-bottom: 1px solid #dedede;
  }
  .close {
    position: absolute;
    top: 14 * @rem;
    right: 14 * @rem;
    width: 14 * @rem;
    height: 14 * @rem;
    background-image: url(~@/assets/images/close-black.png);
    background-size: 100%;
    background-size: no-repeat;
  }
  .content {
    padding: 0 14 * @rem 25 * @rem;
    .item {
      margin-top: 20 * @rem;
    }
    .big-text {
      line-height: 21 * @rem;
      font-size: 16 * @rem;
      .sign {
        font-weight: bolder;
      }
    }
    .small-text {
      margin-top: 5 * @rem;
      line-height: 21 * @rem;
      font-size: 13 * @rem;
      i.icon {
        position: relative;
        top: 3 * @rem;
        display: inline-block;
        width: 16 * @rem;
        height: 16 * @rem;
        margin: 0 5 * @rem;
        background-image: url(~@/assets/images/safari-icon-32x32.png);
        background-size: 100%;
        background-repeat: no-repeat;
        &.icon1 {
          top: 8 * @rem;
          width: 24 * @rem;
          height: 24 * @rem;
          background-image: url(~@/assets/images/tips/tips-icon1.png);
        }
        &.icon2 {
          top: 8 * @rem;
          width: 24 * @rem;
          height: 24 * @rem;
          background-image: url(~@/assets/images/tips/tips-icon2.png);
        }
      }
      .color {
        &.red {
          color: #f63838;
        }
        &.blue {
          color: #51adff;
        }
      }
      .text_style {
        display: inline-block;
        font-size: 17 * @rem;
      }
    }
    .button {
      width: 240 * @rem;
      height: 40 * @rem;
      margin: 20 * @rem auto;
      text-align: center;
      line-height: 40 * @rem;
      font-size: 14 * @rem;
      color: #fff;
      background: @themeBg;
      border-radius: 10 * @rem;
    }
    .explain {
      margin-bottom: 10 * @rem;
      text-align: center;
      color: #999;
    }
  }
}
</style>
