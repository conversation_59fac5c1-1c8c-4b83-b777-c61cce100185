<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    :overlay-style="{ 'z-index': '2998' }"
    class="auth-realname-popup limited-coupon-popup"
  >
    <div class="content" @click="toIdCard()">
      <img class="bg" :src="initData.idcard_img" />
      <!-- <img class="bg" src="@/assets/images/home/<USER>" />
      <div class="button" @click="toIdCard()"></div> -->
    </div>
    <div class="close" @click="setShowLCPopup(false)"></div>
  </van-dialog>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';

export default {
  computed: {
    popup: {
      get() {
        return this.showLCPopup;
      },
      set(value) {
        this.setShowLCPopup(value);
      },
    },
    ...mapGetters({
      showLCPopup: 'user/showLCPopup',
      initData: 'system/initData',
    }),
  },
  methods: {
    toIdCard() {
      this.setShowLCPopup(false);
      this.$router.push({ name: 'IdCard' });
    },
    ...mapMutations({
      setShowLCPopup: 'user/setShowLCPopup',
    }),
  },
};
</script>
<style lang="less" scoped>
.limited-coupon-popup {
  background-color: rgba(0, 0, 0, 0);
  width: 335 * @rem;
  z-index: 2998 !important;
  .content {
    overflow: hidden;
  }
  .bg {
    display: block;
    width: 335 * @rem;
    height: 402 * @rem;
    margin: 55 * @rem auto 0;
  }
  .close {
    width: 35 * @rem;
    height: 36 * @rem;
    margin: 10 * @rem auto 0;
    background: url(~@/assets/images/close.png) center center no-repeat;
    background-size: 26 * @rem 26 * @rem;
  }
  .button {
    position: absolute;
    top: 345 * @rem;
    left: 50%;
    transform: translate(-50%, 0);
    width: 214 * @rem;
    height: 70 * @rem;

    background: url(~@/assets/images/home/<USER>
      no-repeat;
    background-size: 214 * @rem 70 * @rem;
  }
}
</style>
