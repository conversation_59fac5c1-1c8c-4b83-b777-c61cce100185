module.exports = {
  printWidth: 80, // 每行代码的长度
  tabWidth: 2, // 缩进长度
  useTabs: false, // 指定是否使用制表符代替空格缩进
  semi: true, // 句末使用分号
  singleQuote: true, // 使用单引号而不是双引号
  trailingComma: 'all', // 最后一个对象元素加逗号
  vueIndentScriptAndStyle: false, // vue文件script和style标签缩进
  arrowParens: 'avoid', // 箭头函数只有一个参数的时候可以忽略括号
  proseWrap: 'always', // 换行,always：超过printWidth就换行，never：不换行，preserve：按照原样处理
  bracketSpacing: true, // 对象大括号直接是否有空格，默认为true，效果：{ foo: bar }
  htmlWhitespaceSensitivity: 'strict', // 指定 HTML 文件的全局空白区域敏感度 strict: 敏感，ignore: 不敏感，css: 遵守CSS display属性的默认值
  endOfLine: 'auto', // 结尾是 \n \r \n\r auto
  quoteProps: 'consistent', // 对象的属性名是否强制双引号 "" ''
};
/**
 * @quoteProps
 * as-needed：这是默认值，只有在需要时才会给 object 中的键加引号；
 * consistent：如果一个 object 中，至少一项需要加引号，则给所有的键加引号 ；
 * preserve：尊重输入中对于引号的用法（不会自动加或删） 。
 */
