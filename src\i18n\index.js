import Vue from 'vue';
import VueI18n from 'vue-i18n';

Vue.use(VueI18n);

let langFiles = require.context('./config', false, /\.js$/);

let regExp = /^\.\/([^\.]+)\.([^\.]+)$/; //正则用于匹配文件名

let messages = {};

langFiles.keys().forEach(key => {
  let prop = regExp.exec(key)[1]; //正则匹配en|zh这样的值
  //messages[prop]相当于 messages['en']
  messages[prop] = langFiles(key).default;
});

let locale = localStorage.getItem('lang') || 'zh_s';

export default new VueI18n({
  locale,
  fallbackLocale: 'zh_s',
  silentFallbackWar: true,
  messages, // 设置地区信息
});
