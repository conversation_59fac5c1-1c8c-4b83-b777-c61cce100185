import { isIos, isIosBox, isAndroidBox } from '@/utils/userAgent';
import { ApibindWx } from '@/api/views/users';
import router from '@/router';

function platformInit() {
  let platform = ''; // 平台
  try {
    if (isIos && getPostData() != undefined) {
      if (!isIosBox) {
        platform = 'ios'; // ios官包(暂时没有)
      } else {
        platform = 'iosBox'; // ios马甲包
      }
    } else {
      BOX.getFrom();
      if (!isAndroidBox) {
        platform = 'android'; // 安卓官包
      } else {
        platform = 'androidBox'; // 安卓马甲包
      }
    }
  } catch (e) {
    platform = 'h5'; // webapp
  }
  return platform;
}

function fromInit() {
  let from = '';
  switch (platform) {
    case 'ios':
      from = getEval().from;
      break;
    case 'android':
      from = BOX.getFrom();
      break;
    case 'iosBox':
    case 'androidBox':
    case 'h5':
      break;
  }
  return from;
}
export function getToken() {
  let token = '';
  switch (platform) {
    case 'ios':
      token = getEval().token;
      break;
    case 'android':
      token = BOX.getToken();
      break;
    case 'iosBox':
    case 'androidBox':
    case 'h5':
      break;
  }
  return token;
}

export function BOX_getAuthInfo() {
  var result;
  try {
    switch (platform) {
      case 'ios':
      case 'iosBox':
        result = getEval();
        break;
      case 'android':
      case 'androidBox':
        result = JSON.parse(BOX.getAuthInfo());
        break;
      case 'h5':
        result = false;
        break;
    }
  } catch (e) {
    result = false;
  }
  return result;
}

/**
 * @param web webapp相关的参数 如 name(webapp页面的name)、 params(webapp页面的params)、 h5_url
 * @param app app相关的参数 如 url、 page(原生页)
 */
// 在浏览器中打开
export const BOX_openInBrowser = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        window.webkit.messageHandlers.openInBrowser.postMessage(web.h5_url);
        break;
      case 'android':
        BOX.openInBrowser(app.url);
        break;
      case 'androidBox':
        BOX.openInBrowser(web.h5_url);
        break;
      case 'h5':
        if (web.openType) {
          window.open(web.h5_url);
        } else {
          window.location.href = web.h5_url;
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// APP内新窗口打开(不带导航栏)
// ios中的openInNewWindow 会刷新本来的页面(一进去就刷) 隐藏状态栏
// ios中的openInNewWindowHiddenToolBar 不会刷新本来的页面 不隐藏状态栏 --> 隐藏状态栏

export const BOX_openInNewWindow = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewWindowHiddenToolBar.postMessage(
            web.h5_url,
          );
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.openInNewWindowHiddenToolBar(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          //安卓马甲包开启无头新窗口
          BOX.openInNewFullScreenWindow(web.h5_url, web.title);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'h5':
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开带头部的新窗口
export const BOX_openInNewNavWindow = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewNavWindow.postMessage(
            web.h5_url,
          );
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.openInNewWindow(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          BOX.openInNewWindow(web.h5_url);
        } else {
          router.push({ name: web.name, params: web.params });
        }
      case 'h5':
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// APP内新窗口打开(返回时会刷新页面)
export const BOX_openInNewNavWindowRefresh = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewNavWindow.postMessage(
            web.h5_url,
          );
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.openInNewWindowRefresh(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          BOX.openInNewWindowRefresh(web.h5_url);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'h5':
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 关闭窗口
export const BOX_close = refresh => {
  let result;
  try {
    switch (platform) {
      case 'ios':
        window.webkit.messageHandlers.close.postMessage(refresh);
        break;
      case 'iosBox':
        if (web.close) {
          window.webkit.messageHandlers.closeGameWindow.postMessage('close');
        } else {
          router.go(-1);
        }
        break;
      case 'android':
        BOX.close(refresh);
        break;
      case 'androidBox':
        if (web.close) {
          BOX.close(refresh);
        } else {
          router.go(-1);
        }
      case 'h5':
        router.go(-1);
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 页面跳转
 * 每日签到：qd
 * 个人中心：grzx
 * 赚金币：zjb
 * 首页：index
 * 小号回收：xhhs
 * 交易界面：jy
 * 金币转盘：jbzp
 * 下载管理：yygl
 * 金币商城：jbsc
 * 捡漏：jl
 * 游戏试玩 yxsw
 * 游戏内测员 yxncy
 **/
export const BOX_showActivity = (web, app) => {
  // webapp传组件的name
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.isIosBoxToNative) {
          window.webkit.messageHandlers.showActivity.postMessage(app.page);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.showActivity(app.page);
        break;
      case 'androidBox':
        if (web.isAndroidBoxToNative) {
          BOX.showActivity(app.page);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'h5':
        router.push({ name: web.name, params: web.params });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开游戏详情页
export const BOX_goToGame = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.goToGame(app.id);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({ name: 'GameDetail', params: web.params });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开实名认证页面
export const BOX_memAuth = () => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.memAuth();
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({ name: 'IdCard' });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 执行登录
 */
export const BOX_login = () => {
  let result;
  try {
    switch (platform) {
      case 'ios':
        window.webkit.messageHandlers.login.postMessage();
        break;
      case 'iosBox':
        router.push({ name: 'PhoneLogin' });
        break;
      case 'android':
        BOX.login();
        break;
      case 'androidBox':
        router.push({ name: 'PhoneLogin' });
      case 'h5':
        router.push({ name: 'PhoneLogin' });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开微信
export const BOX_openWx = () => {
  try {
    BOX.openApp('com.tencent.mm');
  } catch {
    window.location.href = 'weixin://';
  }
};

// 微信验证
export const Box_wxOAuth2 = () => {
  let result;
  try {
    switch (platform) {
      case 'ios':
      case 'iosBox':
        window.webkit.messageHandlers.wxOAuth2.postMessage({});
        break;
      case 'android':
      case 'androidBox':
        BOX.wxOAuth2();
        break;
      case 'h5':
        Toast('请手动打开微信');
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 马甲包内扭转横竖屏
/**
 * @param {number} code 1横屏0竖屏
 */
export const BOX_setScreenOrientation = code => {
  try {
    switch (platform) {
      case 'iosBox':
        window.webkit.messageHandlers.setScreenOrientation.postMessage(code);
        break;
      case 'androidBox':
        BOX.setScreenOrientation(code);
        break;
      default:
        break;
    }
  } catch {}
};

// 获取当前app包名
export const BOX_getPackageName = () => {
  let result;
  try {
    if (isIos && getPostData() != undefined) {
      result = getEval().packageName;
    } else {
      result = BOX.getPackageName();
    }
  } catch (e) {
    result = false;
  }
  return result;
};

// 获取ios套壳数据
function getPostData() {
  if (!(typeof window.getData == undefined)) {
    return window.getData;
  }
  return undefined;
}

// 遗留下来的方法,ios通讯用
function getEval() {
  let data = getPostData();
  return eval('(' + data + ')');
}

// 获取隐私政策url最后的参数，比如/4sf8u
export function BOX_getProtocolKey() {
  let result;
  try {
    if (isIos && getPostData() != undefined) {
      result = window.webkit.messageHandlers.getProtocolKey.postMessage({});
    } else {
      result = BOX.getProtocolKey();
    }
  } catch (e) {
    result = '4sf8u';
  }
  return result;
}

// 微信认证回调
window.getWxUserInfo = async params => {
  const res = await ApibindWx({
    wxCode: params.wxCode,
  });
  if (res.code >= 1) {
    Toast(`绑定成功${res.data.wx_nickname}`);
  }
};

export let platform = platformInit();
export let from = fromInit();
export let isIosSdk = platform == 'ios' && from == 103 ? true : false;
export let isAndroidSdk = platform == 'android' && from == 3 ? true : false;
export let isSdk = isAndroidSdk || isIosSdk ? true : false;
export let authInfo = BOX_getAuthInfo();
export let packageName = BOX_getPackageName();
