<template>
  <div class="game-item-new">
    <game-item-2 :gameInfo="info" :type="type"></game-item-2>
    <div class="video-container" v-if="info.video_url">
      <video
        :src="info.video_url"
        :poster="info.video_thumb"
        :webkit-playsinline="true"
        :playsinline="true"
        :x5-playsinline="true"
        x-webkit-airplay="allow"
      >
        {{ $t('您的手机不支持该视频文件！！！') }}
      </video>
      <div v-if="info.video_url" @click="play($event)" class="play"></div>
    </div>
    <!-- 没有视频改为一张封面图 2022年8月16日13:49:11 -->
    <div class="video-container" v-else-if="info.titleimg">
      <img :src="info.titleimg" alt="" />
    </div>
    <div class="pic-container" v-else-if="info.morepic">
      <template v-for="(item, index) in info.morepic.small">
        <img
          :src="item"
          alt=""
          :key="index"
          v-if="index < 3"
          @click="showBigImage(info.morepic.big, index)"
        />
      </template>
    </div>
    <div class="game-smalltext">{{ info.yxftitle }}</div>
  </div>
</template>

<script>
import { ImagePreview } from 'vant';
export default {
  name: 'gameItemNew',
  props: {
    info: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      default: 0,
    },
    type: {
      type: Number,
      default: 1,
    },
  },
  methods: {
    play($event) {
      this.pauseAll();
      let $play = $event.target;
      let $video = $play.parentNode.querySelector('video');
      $play.style.display = 'none';
      $video.play();
      $video.setAttribute('controls', 'true');
    },
    pauseAll() {
      let $videos = document.querySelectorAll('.game-item-new video');
      let $plays = document.querySelectorAll('.game-item-new .play');
      $videos.forEach(item => {
        item.pause();
        item.removeAttribute('controls');
      });
      $plays.forEach(item => {
        item.style.display = 'block';
      });
    },
    // 截图查看大图
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.game-item-new {
  box-sizing: border-box;
  width: 339 * @rem;
  background: #ffffff;
  box-shadow: 0 * @rem 3 * @rem 11 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
  border-radius: 10 * @rem;
  margin: 0 auto;
  margin-top: 10 * @rem;
  padding: 13 * @rem;
  .pic-container {
    width: 313 * @rem;
    max-height: 176 * @rem;
    position: relative;
    margin: 3 auto 0;
    border-radius: 8 * @rem;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    img {
      width: 100 * @rem;
      border-radius: 10 * @rem;
    }
  }
  .video-container {
    width: 313 * @rem;
    height: 176 * @rem;
    position: relative;
    margin: 3 auto 0;
    border-radius: 8 * @rem;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 45 * @rem;
      height: 45 * @rem;
      background-image: url(~@/assets/images/video-play.png);
      background-size: 100%;
    }
  }
  .game-smalltext {
    font-size: 13 * @rem;
    color: #797979;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 10 * @rem;
  }
}
</style>
