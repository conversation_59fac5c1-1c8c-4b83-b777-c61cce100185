<template>
  <div class="share-all-page">
    <rubber-band :topColor="'#151412'" :bottomColor="'#151412'">
      <ShareDownBox />
      <div class="single-container">
        <loading-indicator
          v-if="!isLoading"
          :class="{
            centered: !isLoading,
          }"
        />
        <template v-else>
          <div class="share-user">
            <div
              class="user-info"
              v-if="shareUserInfo && Object.keys(shareUserInfo).length > 0"
            >
              <div class="user-avatar">
                <img
                  v-if="shareUserInfo.avatar"
                  :src="shareUserInfo.avatar"
                  alt=""
                />
                <img v-else :src="defaultAvatar" alt="" />
              </div>
              <div class="user-name">{{ shareUserInfo.nickname }}</div>
            </div>
          </div>
          <div
            class="main"
            :class="{
              centered:
                (badgeList &&
                  Object.keys(badgeList).length == 1 &&
                  currentBadge.record_id == 0) ||
                !Object.keys(badgeList).length,
            }"
          >
            <div
              class="empty-box"
              v-if="
                (badgeList &&
                  Object.keys(badgeList).length == 1 &&
                  currentBadge.record_id == 0) ||
                !Object.keys(badgeList).length
              "
            >
              <default-not-found-page notFoundText="没有该徽章哦～" />
            </div>
            <template v-else>
              <div
                class="award-record-box"
                v-for="yearData in awardRecordInfo"
                :key="yearData.year"
              >
                <div class="step-title">{{ yearData.year }}</div>
                <div class="step-list">
                  <template v-for="monthData in yearData.months">
                    <div class="step-sub-title" :key="monthData.month">{{
                      monthData.month
                    }}</div>
                    <div class="step-item" :key="`item-${monthData.month}`">
                      <div class="step-card">
                        <div class="badge-box">
                          <div
                            class="badge-content"
                            v-for="(item, index) in monthData.badges"
                            :key="index"
                          >
                            <div class="badge-item">
                              <div class="content">
                                <div class="badge-image">
                                  <img :src="item.icon_url" />
                                </div>
                                <div class="badge-name">{{ item.name }}</div>
                                <div class="badge-time"
                                  >{{ formatDate(item.create_time) }} 获得</div
                                ></div
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
      <div class="share-btn">
        <span class="btn" @click="handleDownload()">查看我的徽章</span>
      </div>
    </rubber-band>
  </div>
</template>

<script>
import ShareDownBox from './components/share-down-box.vue';
import LoadingIndicator from '@/components/loading-Indicator';
import { ApiUserBadgeShare } from '@/api/views/badgeCollection.js';
export default {
  name: 'ShareAll',
  props: {},
  components: {
    ShareDownBox,
    LoadingIndicator,
  },
  data() {
    return {
      title: '',
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      layerBoxOpacity: 1,
      swiperBannerOptions: {
        slidesPerView: 2,
        centeredSlides: true,
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerGroup: 1, // 一次滑动一个
        slideToClickedSlide: true,
        spaceBetween: 20,
        loop: false,
        resistanceRatio: 0, // 不允许边界回弹
        observer: true,
        observeSlideChildren: true,
      },
      badgeList: [],
      awardRecordInfo: [],
      currentBadge: {},
      currentIndex: 0,
      isLoading: false,
      shareUserInfo: {},
      params: {
        id: 0,
        share_type: 15,
        record_id: 0,
        mem_id: 0,
        listRow: 30,
      },
    };
  },
  created() {
    this.params.id = this.$route.query.id;
    this.params.record_id = this.$route.query.record_id || 0;
    this.params.mem_id = Number(this.$route.query.mem_id) || 0;
    this.getUserBadgeShareList();
  },
  mounted() {},
  methods: {
    handleDownload() {
      window.location.href = 'https://app.3733.com/';
    },
    async getUserBadgeShareList() {
      try {
        const res = await ApiUserBadgeShare({
          id: this.params.id,
          share_type: this.params.share_type,
          record_id: this.params.record_id,
          mem_id: this.params.mem_id,
          listRow: this.params.listRow,
        });
        let { list } = res.data;
        this.awardRecordInfo = this.formatAwardData(list);
        this.badgeList = [res.data.badge] || [];
        this.currentBadge = res.data.badge || {};
        this.shareUserInfo = res.data.user || {};
      } catch (error) {
      } finally {
        this.isLoading = true;
      }
    },
    formatDate(val) {
      let { year, month, day } = this.$handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },
    // 处理徽章时间线
    formatAwardData(data) {
      const result = [];

      data.sort((a, b) => b.create_time - a.create_time);

      data.forEach(item => {
        const date = new Date(item.create_time * 1000);
        const year = date.getFullYear() + '年';
        const month = date.getMonth() + 1 + '月';

        // 构建 badge 对象
        const badge = item;

        // 查找年份
        let yearGroup = result.find(y => y.year == year);
        if (!yearGroup) {
          yearGroup = {
            year,
            months: [],
          };
          result.push(yearGroup);
        }

        // 查找月份
        let monthGroup = yearGroup.months.find(m => m.month == month);
        if (!monthGroup) {
          monthGroup = {
            month,
            badges: [],
          };
          yearGroup.months.push(monthGroup);
        }

        monthGroup.badges.push(badge);
      });

      return result;
    },
  },
  beforeDestroy() {},
};
</script>

<style lang="less" scoped>
.share-all-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #151412 0%, #151412 100%);
  display: flex;
  /deep/.rubber-band {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .top-bg {
    width: 100%;
    height: 358 * @rem;
    background: url(~@/assets/images/badge-collection/badge-collection-bg2.png)
      no-repeat top right;
    background-size: 100% auto;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    .layer-box {
      position: relative;
      z-index: 5;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 127 * @rem;
      height: 127 * @rem;
      transition: opacity 0.3s ease;
      .myParticleCanvas {
        width: 100%;
        height: 100%;
      }
    }
  }
  .single-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    &.centered {
      display: flex;
      flex-direction: column;
      align-content: center;
      justify-content: center;
    }
    .share-user {
      position: relative;
      padding-top: calc(104 * @rem + @safeAreaTop);
      padding-top: calc(104 * @rem + @safeAreaTopEnv);
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .user-info {
        margin-left: 25 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .user-avatar {
          width: 46 * @rem;
          height: 46 * @rem;
          border: 2px solid rgba(255, 255, 255, 0.4);
          border-radius: 50%;
          overflow: hidden;
          background: #ccc;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .user-name {
          margin-left: 8 * @rem;
          font-weight: bold;
          font-size: 16 * @rem;
          color: #fffcec;
        }
      }
    }
    .main {
      flex: 1;
      padding-bottom: 30 * @rem;
      &.centered {
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;
      }
      .award-record-box {
        padding: 0 27 * @rem 0 25 * @rem;
        box-sizing: border-box;
        &:first-of-type {
          margin-top: 30 * @rem;
        }
        &:not(:first-of-type) {
          margin-top: 22 * @rem;
        }
        .step-title {
          margin-left: 6 * @rem;
          font-weight: 600;
          font-size: 20 * @rem;
          color: #fffcec;
        }
        .step-list {
          position: relative;
          display: flex;
          flex-direction: column;
          &::before {
            content: '';
            position: absolute;
            left: 3 * @rem;
            top: 20 * @rem;
            width: 0;
            height: calc(100% - 20 * @rem);
            border-left: 1 * @rem solid #3d3b35;
          }
          .step-sub-title {
            position: relative;
            margin: 12 * @rem 0 12 * @rem 0;
            font-weight: 400;
            font-size: 16 * @rem;
            color: #fffcec;
            padding-left: 14 * @rem;
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 6 * @rem;
              height: 6 * @rem;
              background: #3d3b35;
              border-radius: 50%;
            }
          }
          .step-item {
            padding: 0 0 0 25 * @rem;
            .step-card {
              .badge-box {
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                .badge-content {
                  margin-bottom: 16 * @rem;
                  &:nth-of-type(3n) {
                    .badge-item {
                      margin-right: 0;
                    }
                  }
                  .badge-item {
                    width: 75 * @rem;
                    margin-right: 36 * @rem;
                    overflow: hidden;
                    .content {
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      .badge-image {
                        width: 75 * @rem;
                        height: 75 * @rem;
                      }
                      .badge-name {
                        height: 20 * @rem;
                        line-height: 20 * @rem;
                        font-weight: 400;
                        font-size: 14 * @rem;
                        color: #fffcec;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 75 * @rem;
                      }
                      .badge-time {
                        height: 14 * @rem;
                        line-height: 14 * @rem;
                        font-weight: 400;
                        font-size: 10 * @rem;
                        color: #6e6b64;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 75 * @rem;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .share-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 116 * @rem;
    span {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 46 * @rem;
      background: linear-gradient(90deg, #fcf7d1 0%, #f7d7b0 100%);
      border-radius: 23 * @rem;
      white-space: nowrap;
      font-weight: bold;
      font-size: 16 * @rem;
      color: #a25100;
      padding: 0 45 * @rem;
    }
  }
}
</style>
