export default {
  state: {
    gameInfo: {}, //游戏详情
    smallAccountName: '', //小号名称
    smallAccountId: '', //小号ID
    time: '', //充值时间
  },
  getters: {
    gameInfo(state) {
      return state.gameInfo;
    },
    smallAccountName(state) {
      return state.smallAccountName;
    },
    smallAccountId(state) {
      return state.smallAccountId;
    },
    time(state) {
      return state.time;
    },
  },
  mutations: {
    setGameInfo(state, gameInfo) {
      state.gameInfo = gameInfo;
    },
    setSmallAccountName(state, smallAccountName) {
      state.smallAccountName = smallAccountName;
    },
    setSmallAccountId(state, smallAccountId) {
      state.smallAccountId = smallAccountId;
    },
    setTime(state, time) {
      state.time = time;
    },
    setInit(state, gameInfo) {
      state.gameInfo = gameInfo;
      state.smallAccountName = '';
      state.smallAccountId = '';
      state.time = '';
    },
  },
  actions: {},
};
