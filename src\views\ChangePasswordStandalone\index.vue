<template>
  <div class="forget-password">
    <div class="top-bg">
      <img
        src="~@/assets/images/change-password-standalone/top-img.png"
        alt=""
      />
    </div>
    <div class="main-content">
      <div class="hand"></div>
      <div class="item">
        <div class="title">手机号</div>
        <div class="phone input-box">
          <div class="area-code" @click="toPage('AreaCode')">
            <span>+{{ areaCode }}</span>
          </div>
          <div class="line"></div>
          <input
            type="number"
            v-model="phone"
            name="phoneNumber"
            placeholder="请输入手机号"
          />
        </div>
        <div class="verification input-box">
          <input
            type="number"
            name="v-code"
            v-model="authCode"
            max="999999"
            min="0"
            placeholder="请输入验证码"
          />
          <div class="line"></div>
          <div class="get-code" v-if="!ifCount" @click="getAuthCode()">
            获取验证码
          </div>
          <div class="get-code" v-else>{{ `重新获取${countdown}s` }}</div>
        </div>
      </div>
      <div class="item">
        <div class="title">请设置密保问题</div>
        <div class="question input-box">
          <security-question-bar
            class="field-content question-box"
            :selectedQuestion.sync="selectedQuestion"
          ></security-question-bar>
        </div>
        <div class="title">请设置密保答案</div>
        <div class="answer input-box">
          <input
            type="text"
            v-model="answer"
            name="answer"
            placeholder="最长可输入10个字符"
            maxlength="10"
          />
        </div>
      </div>
      <div class="item">
        <div class="title">设置新密码</div>
        <div class="password input-box">
          <input
            :type="open ? 'text' : 'password'"
            name="pwd"
            v-model="password"
            placeholder="请输入新密码"
          />
          <div
            class="show-pwd"
            v-if="password.length > 0"
            :class="{ open: open }"
            @click="open = !open"
          ></div>
        </div>
        <div class="password input-box">
          <input
            :type="reOpen ? 'text' : 'password'"
            name="re-pwd"
            v-model="rePassword"
            placeholder="请再次输入新密码"
          />
          <div
            class="show-pwd"
            v-if="rePassword.length > 0"
            :class="{ open: reOpen }"
            @click="reOpen = !reOpen"
          ></div>
        </div>
      </div>
    </div>
    <div class="submit-btn btn" @click="commit()">提交修改</div>
    <div class="popup-box" v-if="successShow">
      <div class="content-box">
        <div class="icon success"></div>
        <div class="text">修改密码成功！</div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiUserSetSecurityByPhone, ApiAuthCode } from '@/api/views/users';
import securityQuestionBar from '@/components/security-question-bar';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'ChangePassword',
  components: {
    securityQuestionBar,
  },
  data() {
    return {
      phone: '',
      authCode: '',
      password: '',
      rePassword: '',
      countdown: 60,
      ifCount: false,
      open: false, //是否显示密码
      reOpen: false, //是否显示密码
      selectedQuestion: {
        title: '请选择问题',
      },
      answer: '',
      successShow: false,
    };
  },
  computed: {
    ...mapGetters({
      areaCode: 'user/areaCode',
      initData: 'system/initData',
    }),
  },
  created() {
    if (!this.areaCode) {
      this.setAreaCode(86);
    }
  },
  methods: {
    ...mapMutations({
      setAreaCode: 'user/setAreaCode',
    }),
    getAuthCode() {
      if (this.phone === '') {
        this.$toast('请输入手机号码');
        return false;
      }

      // 发送axios请求
      let params = {
        phone: this.phone,
        type: 3,
        countryCode: this.areaCode,
      };
      ApiAuthCode(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
    async commit() {
      if (this.phone === '') {
        this.$toast('请输入手机号码');
        return false;
      }
      if (this.authCode === '') {
        this.$toast('请输入获得的验证码!');
        return false;
      }

      if (this.answer === '') {
        this.$toast('请输入密保答案');
        return false;
      }
      if (!this.selectedQuestion.id) {
        this.$toast('请选择问题');
        return false;
      }

      if (this.password === '') {
        this.$toast('请输入新密码');
        return false;
      }
      if (this.password != this.rePassword) {
        this.$toast('两次输入的密码不同，请重新输入');
        return false;
      }

      this.$toast.loading({
        message: '重置密码中...',
        forbidClick: true,
        duration: 0,
      });

      // 提交逻辑
      let params = {
        password: this.password,
      };
      params.phone = this.phone;
      params.code = this.authCode;
      params.question_1 = this.selectedQuestion.id;
      params.answer_1 = this.answer;
      params.countryCode = this.areaCode;
      console.log(params);
      await ApiUserSetSecurityByPhone(params);
      this.$toast.clear();
      this.successShow = true;
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    },
  },
};
</script>

<style lang="less" scoped>
.forget-password {
  padding-top: 44px;
  background: #f8f8f8
    linear-gradient(
      170deg,
      #1dc25a 0%,
      rgba(69, 210, 79, 0.82) 49%,
      rgba(70, 212, 79, 0) 100%
    );
  overflow: hidden;
}

input {
  background-color: transparent;
}

.top-bg {
  display: block;
  width: 351.5 * @rem;
  margin-left: 13 * @rem;

  img {
    display: block;
    width: 100%;
  }
}

.main-content {
  display: block;
  width: 345 * @rem;
  background-color: #fff;
  border-radius: 26 * @rem;
  padding: 20 * @rem 20 * @rem 30 * @rem;
  box-sizing: border-box;
  margin: 0 auto;
  margin-top: -63 * @rem;
  position: relative;
  z-index: 1;

  .hand {
    display: block;
    width: 27 * @rem;
    height: 23 * @rem;
    background: url(~@/assets/images/change-password-standalone/hand.png)
      no-repeat;
    background-size: cover;
    position: absolute;
    top: -12 * @rem;
    right: 32 * @rem;
  }

  .item {
    margin-bottom: 30 * @rem;

    &:last-of-type {
      margin-bottom: 0;
    }

    .title {
      display: block;
      height: 22 * @rem;
      font-size: 16 * @rem;
      font-weight: 500;
      color: #363636;
      line-height: 22 * @rem;
      padding-left: 10 * @rem;
      margin-bottom: 16 * @rem;
    }

    .input-box {
      display: flex;
      align-items: center;
      width: 100%;
      height: 50 * @rem;
      background: #f8f8f8;
      border-radius: 100 * @rem;
      margin-bottom: 16 * @rem;
      position: relative;

      &:last-of-type {
        margin-bottom: 0;
      }

      input {
        display: block;
        flex: 1;
        min-width: 0;
        padding: 0 20 * @rem;
        height: 50 * @rem;
        line-height: 50 * @rem;
        font-size: 16 * @rem;
        font-weight: 500;
        color: #363636;
        line-height: 16 * @rem;
        border-radius: 100 * @rem;

        &[name='phoneNumber'] {
          padding: 0 16 * @rem;
        }
      }

      .line {
        display: block;
        flex-shrink: 0;
        width: 1 * @rem;
        height: 24 * @rem;
        background: #e8e8e8;
      }

      .area-code {
        display: block;
        flex-shrink: 0;
        width: 65 * @rem;
        height: 16 * @rem;
        font-size: 16 * @rem;
        font-weight: 500;
        color: #363636;
        line-height: 16 * @rem;
        text-align: center;
      }

      .get-code {
        display: block;
        flex-shrink: 0;
        width: 111 * @rem;
        height: 50 * @rem;
        font-size: 15 * @rem;
        font-weight: 500;
        color: #3ac763;
        line-height: 50 * @rem;
        text-align: center;
        cursor: pointer;
      }

      .question-box {
        width: 100%;
        /deep/ .question-bar {
          width: 100%;
          .question-container {
            background-color: transparent;
            border: none;
            width: 100%;
            padding: 0 20 * @rem;
          }
        }
      }

      .show-pwd {
        display: block;
        flex-shrink: 0;
        width: 32 * @rem;
        height: 32 * @rem;
        margin-right: 10 * @rem;
        background: url(~@/assets/images/change-password-standalone/eye-off.png)
          no-repeat center center;
        background-size: 22 * @rem 22 * @rem;

        &.open {
          background-image: url(~@/assets/images/change-password-standalone/eye.png);
        }
      }
    }
  }
}

.btn {
  display: block;
  width: 295 * @rem;
  height: 48 * @rem;
  line-height: 48 * @rem;
  background: linear-gradient(139deg, #26c749 0%, #43df65 100%);
  border-radius: 25 * @rem;
  margin: 30 * @rem auto 35 * @rem;
  font-size: 16 * @rem;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
}

.popup-box {
  display: block;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  background-color: rgba(24, 24, 24, 0.75);

  .content-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 231 * @rem;
    height: 182 * @rem;
    background: #ffffff;
    border-radius: 39 * @rem;
    border: 3 * @rem solid #18d942;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .icon {
      display: block;
      width: 65 * @rem;
      height: 65 * @rem;
      background: url(~@/assets/images/change-password-standalone/success.png)
        no-repeat;
      background-size: 65 * @rem 65 * @rem;
    }

    .text {
      display: block;
      height: 16 * @rem;
      font-size: 16 * @rem;
      font-weight: 500;
      color: #363636;
      line-height: 16 * @rem;
      text-align: center;
      margin-top: 16 * @rem;
    }
  }
}

.toast {
  display: none;
  max-width: 280 * @rem;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 44 * @rem;
  padding: 7 * @rem 20 * @rem;
  font-size: 14 * @rem;
  font-weight: 400;
  color: #ffffff;
  line-height: 20 * @rem;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99;
}
</style>
