<template>
  <div>
    <van-popover v-model="questionPopup" trigger="click" class="question-bar">
      <template #reference>
        <div class="field question-container">
          <div class="question-text">{{ selectedQuestion.title }}</div>
          <div class="down-arrow" :class="{ up: questionPopup }"></div>
        </div>
      </template>
      <div class="question-list">
        <div
          class="question-item"
          v-for="(item, index) in questionActions"
          :key="index"
          @click="handleSelectQuestion(item)"
        >
          {{ item.title }}
        </div>
      </div>
    </van-popover>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
export default {
  props: {
    selectedQuestion: {
      type: Object,
      default: () => {
        return {
          title: '请选择问题',
        };
      },
    },
  },
  data() {
    return {
      questionPopup: false,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    selected: {
      get() {
        return this.selectedQuestion;
      },
      set(val) {
        this.$emit('update:selectedQuestion', val);
      },
    },
    questionActions() {
      return this.initData.question_list;
    },
  },
  methods: {
    // 监听密保问题选择
    onQuestionSelect(e) {
      if (e.id) {
        this.selected = e;
      }
    },
    openPopup() {
      this.questionPopup = true;
    },
    handleSelectQuestion(item) {
      this.selected = item;
      this.questionPopup = false;
    },
  },
};
</script>

<style lang="less" scoped>
.question-bar {
  width: 316 * @rem;
}
.question-container {
  box-sizing: border-box;
  width: 316 * @rem;
  height: 40 * @rem;
  background: #fbfbfb;
  border: 0.5 * @rem solid #e0e0e0;
  border-radius: 6 * @rem;
  display: flex;
  align-items: center;
  padding: 0 11 * @rem;
  .question-text {
    flex: 1;
    min-width: 0;
    font-size: 14 * @rem;
    color: #10141f;
  }
  .down-arrow {
    width: 14 * @rem;
    height: 14 * @rem;
    background: url(~@/assets/images/users/arrow-down-orange.png) center center
      no-repeat;
    background-size: 14 * @rem 14 * @rem;
    transform: rotateX(180deg);
    transition: 0.5s all;
    &.up {
      transform: rotateX(0deg);
    }
  }
}
.question-list {
  box-sizing: border-box;
  width: 316 * @rem;
  max-height: 220 * @rem;
  padding: 7 * @rem 20 * @rem;
  overflow-y: auto;
  .question-item {
    height: 40 * @rem;
    line-height: 40 * @rem;
    font-size: 14 * @rem;
    color: #333333;
    overflow: auto;
    white-space: nowrap;
    &:not(:last-child) {
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
