<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    :overlay-style="{ 'z-index': '2996' }"
    class="birthday-popup"
  >
    <div class="title">生日福利</div>
    <div class="content" v-if="birthdayData">
      {{ birthdayData }}
    </div>
    <div class="confirm" @click="confirm">确定</div>
    <div class="tips" @click="remember = !remember">
      <div class="gou" :class="{ remember: remember }"></div>
      <div class="tips-text">不再提示</div>
    </div>
  </van-dialog>
</template>
<script>
import { mapMutations, mapGetters, mapActions } from 'vuex';
export default {
  data() {
    return {
      remember: false,
    };
  },
  computed: {
    popup: {
      get() {
        return this.showBirthdayPopup;
      },
      set(value) {
        this.setShowBirthdayPopup(value);
      },
    },
    ...mapGetters({
      showBirthdayPopup: 'user/showBirthdayPopup',
      birthdayData: 'user/birthdayData',
    }),
  },
  methods: {
    confirm() {
      if (this.remember) {
        this.setShowBirthdayPopup(false);
        this.SET_BIRTHDAY_POPUP(1);
      } else {
        this.setShowBirthdayPopup(false);
      }
    },
    ...mapMutations({
      setShowBirthdayPopup: 'user/setShowBirthdayPopup',
    }),
    ...mapActions({
      SET_BIRTHDAY_POPUP: 'user/SET_BIRTHDAY_POPUP',
    }),
  },
};
</script>
<style lang="less" scoped>
.birthday-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  padding: 20 * @rem 0 25 * @rem;
  background: #fff url(~@/assets/images/game-activity/popup-bg.png) center top
    no-repeat;
  background-size: 300 * @rem auto;
  border-radius: 20 * @rem;
  z-index: 2996 !important;
  .title {
    font-size: 20 * @rem;
    color: #fe6600;
    text-align: center;
    font-weight: bold;
  }
  .content {
    line-height: 17 * @rem;
    font-size: 12 * @rem;
    color: #777777;
    padding: 0 25 * @rem;
    margin: 22 * @rem auto 0;
  }
  .confirm {
    width: 102 * @rem;
    height: 35 * @rem;
    border-radius: 18 * @rem;
    margin: 12 * @rem auto 0;
    background: #fe6600;
    font-size: 13 * @rem;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .tips {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10 * @rem auto 0;
    .gou {
      width: 12 * @rem;
      height: 12 * @rem;
      background: url(~@/assets/images/turn-table/gou.png) center center
        no-repeat;
      background-size: 12 * @rem 12 * @rem;
      &.remember {
        background-image: url(~@/assets/images/turn-table/gou-on.png);
      }
    }
    .tips-text {
      font-size: 12 * @rem;
      color: #999999;
      margin-left: 6 * @rem;
    }
  }
}
</style>
