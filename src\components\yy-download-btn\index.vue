<template>
  <div class="yy-download-btn" @click.stop="handleClick()" v-if="dlConfig != 1">
    <slot>
      <div class="download-btn" :class="{ 'h5-url': gameInfo.h5_url }">
        {{ buttonText }}
      </div>
    </slot>
  </div>
</template>

<script>
import { downloadGame, startCloudGame, startH5Game } from '@/utils/function.js';
import { isIos, isAndroid } from '@/utils/userAgent';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'yyDownloadBtn',
  props: {
    gameInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      isIos,
      isAndroid,
    };
  },
  computed: {
    ...mapGetters({
      dlConfig: 'system/dlConfig',
    }),
    // 是否显示云玩
    showCloud() {
      return this.gameInfo.yyx_type
        ? parseInt(this.gameInfo.yyx_type.code) !== 0 && isIos
        : false;
    },
    // 按钮文案
    buttonText() {
      if (this.isIos && this.gameInfo.dl_config_i == 1) {
        return this.$t('下载');
      } else if (this.gameInfo.h5_url) {
        return this.$t('开始玩');
      } else if (this.showCloud) {
        return this.$t('云玩');
      } else {
        return this.$t('下载');
      }
    },
  },
  methods: {
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
    }),
    handleClick() {
      this.setGameInfo(this.gameInfo);
      if (this.isIos && this.gameInfo.dl_config_i == 1) {
        downloadGame(this.gameInfo);
      } else if (this.gameInfo.h5_url) {
        this.startGame(this.gameInfo);
      } else if (this.showCloud) {
        startCloudGame(this.gameInfo.id);
      } else {
        downloadGame(this.gameInfo);
      }
    },
    // 开始游戏
    startGame(gameInfo) {
      this.$toast.loading({
        message: '加载中...',
        duration: 1000,
      });
      if (!this.userInfo.token) {
        this.$toast.clear();
      }
      startH5Game(gameInfo.h5_url, gameInfo.app_id);
    },
  },
};
</script>

<style lang="less" scoped>
.yy-download-btn {
  .download-btn {
    box-sizing: border-box;
    width: 58 * @rem;
    height: 30 * @rem;
    font-size: 12 * @rem;
    color: #fe6600;
    border-radius: 6 * @rem;
    border: 1px solid #fe6600;
    display: flex;
    align-items: center;
    justify-content: center;
    &.h5-url {
      width: 58 * @rem;
      height: 30 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: #fffcfa;
      background: #ff7e53;
      border-radius: 6 * @rem;
      border: none;
    }
  }
}
</style>
