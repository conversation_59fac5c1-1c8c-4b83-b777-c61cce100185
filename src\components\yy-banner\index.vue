<template>
  <!-- 轮播 -->
  <van-swipe
    class="yy-swiper"
    :class="{ collect: type == 2 }"
    :autoplay="5000"
    :indicator-color="indicatorColor"
    :style="{
      width: `${width * remNumberLess}rem !important`,
      height: `${height * remNumberLess}rem !important`,
    }"
    v-if="bannerList.length"
  >
    <template v-for="banner in bannerList">
      <!-- 游戏 -->
      <van-swipe-item
        class="swiper-slide"
        :key="banner.id"
        v-if="banner.type == 1"
        :style="{
          width: `${width * remNumberLess}rem !important`,
          height: `${height * remNumberLess}rem !important`,
        }"
        @click="tapBanner(banner)"
      >
        <img class="banner" :src="banner.titleimg" />
      </van-swipe-item>
      <!-- 活动 -->
      <van-swipe-item
        class="swiper-slide"
        :key="banner.id"
        v-else-if="banner.type == 4"
        :style="{
          width: `${width * remNumberLess}rem !important`,
          height: `${height * remNumberLess}rem !important`,
        }"
        @click="tapBanner(banner)"
      >
        <img class="banner" :src="banner.titleimg" />
      </van-swipe-item>
      <!-- 捡漏等原生页面 -->
      <van-swipe-item
        class="swiper-slide"
        :key="banner.id"
        v-else
        :style="{
          width: `${width * remNumberLess}rem !important`,
          height: `${height * remNumberLess}rem !important`,
        }"
        @click="tapBanner(banner)"
      >
        <img class="banner" :src="banner.titleimg" />
      </van-swipe-item>
      <!-- 首页推荐合集的轮播 -->
      <van-swipe-item
        class="swiper-slide"
        :key="banner.id"
        v-if="type == 2"
        :style="{
          width: `${width * remNumberLess}rem !important`,
          height: `${height * remNumberLess}rem !important`,
        }"
        @click="tapBanner(banner)"
      >
        <img class="banner" :src="banner.list_banner" />
        <div class="banner-title" v-if="banner.title">{{ banner.title }}</div>
      </van-swipe-item>
    </template>
  </van-swipe>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { clickBanner } from '@/utils/function.js';
import { ApiStatisticsBanner } from '@/api/views/home.js';
export default {
  name: 'YyBanner',
  data() {
    return {
      remNumberLess,
    };
  },
  props: {
    bannerList: {
      type: Array,
      default: () => [],
    },
    indicatorColor: {
      type: String,
      default: '#FAAE86',
    },
    width: {
      type: Number,
      default: 340, // 单位rem
    },
    height: {
      type: Number,
      default: 190, // 单位rem
    },
    type: {
      // 1-轮播标准数据类型 2-首页推荐合集
      type: Number,
      default: 1,
    },
  },
  methods: {
    tapBanner(banner) {
      this.$point('首页', '点击', 'banner');
      try {
        ApiStatisticsBanner({ id: banner.id });
      } catch (e) {
        console.log(e);
      }
      if (this.type === 2) {
        // 首页推荐合集
        this.toPage('GameCollect', { id: banner.id, info: banner });
        return false;
      }
      clickBanner(banner);
    },
  },
};
</script>
<style lang="less" scoped>
.yy-swiper {
  width: 340 * @rem;
  height: 190 * @rem;
  margin: 10 * @rem auto 0;
  border-radius: 10 * @rem;
  overflow: hidden;
  &.collect {
    padding-bottom: 13 * @rem;
    /deep/ .van-swipe__indicators {
      bottom: 0 * @rem;
      .van-swipe__indicator {
        background-color: #ebebeb;
      }
    }
  }
  .swiper-slide {
    position: relative;
    width: 340 * @rem;
    height: 190 * @rem;
    border-radius: 10 * @rem;
    overflow: hidden;
    img {
      object-fit: cover;
    }
    .banner-title {
      box-sizing: border-box;
      height: 60 * @rem;
      width: 100%;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.7) 100%
      );
      position: absolute;
      bottom: 0;
      left: 0;
      color: #fff;
      line-height: 22 * @rem;
      font-size: 16 * @rem;
      font-weight: bold;
      padding: 13 * @rem;
      padding-top: 28 * @rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  /deep/ .van-swipe__indicator {
    width: 5 * @rem;
    height: 5 * @rem;
    background-color: #fff;
    opacity: 1;
    border-radius: 3 * @rem;
  }
  /deep/ .van-swipe__indicator--active {
    width: 13 * @rem;
    height: 5 * @rem;
    background-color: #faae86;
  }
  /deep/ .van-swipe__indicator:not(:last-child) {
    margin-right: 3 * @rem;
  }
}
</style>
