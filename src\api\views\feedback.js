import { request } from '../index';

/* 
投诉反馈提交(旧)
object 投诉对象,
object_tel 187633333,
user 投诉人,
user_tel 投诉人联系方式,
content 填写投诉的具体内容
photos 拼接图片地址  用,隔开  地址全部是上传的接口返回的
*/
export function ApiComplainFeedbackPost(params = {}) {
  return request('/web/feedback/complainFeedbackPost', params);
}

/**
 * 投诉反馈提交（新）
 * @param {type} 类型 1=游戏相关 2=下载安装 3=投诉客服 4=其他建议
 * @param {user_tel} 反馈人手机
 * @param {user_qq} 反馈人QQ号
 * @param {content} 反馈内容
 * @param {photos}
 * 类型为投诉客服 增加下面三个参数
 * @param {object} 投诉对象
 * @param {object_tel} 187633333
 * @param {user_name} 投诉人称呼
 * 类型为游戏相关 增加游戏id参数
 * @param {game_id} 游戏id参数
 */
export function ApiFeedbackPost(params = {}) {
  return request('/web/feedback/feedbackPost', params);
}

/**
 * 反馈通知
 * @param {page} 1
 * @param {listRows} 10
 */
export function ApiFeedbackInform(params = {}) {
  return request('/web/feedback/inform', params);
}

/* 
游戏反馈 - 页面
  isHide  0 游戏反馈过来的多传个这个，区分投诉反馈
  gameId  如果是游戏详情的反馈，需传游戏id
*/
export function ApiGameFeedback(params = {}) {
  return request('/web/feedback/gameFeedback', params);
}

/* 
游戏反馈提交
  gameId  游戏id
  gameTitle  游戏标题
  reportCate 举报类型（例如：4）
  content  问题描述
  contact  联系方式
  extraJson 包含额外信息的Json：device等
  photos  拼接图片地址  用,隔开  地址全部是上传的接口返回的
  isHide  0 游戏反馈过来的多传个这个，区分投诉反馈
*/
export function ApiGameFeedbackReport(params = {}) {
  return request('/api/common/report/', params);
}

/**
 * 反馈全部标记已读
 */
export function ApiFeedbackRead(params = {}) {
  return request('/web/feedback/feedbackRead', params);
}

/**
 * 客服页的热门问题
 */
export function ApiFeedGetTopQuestions(params = {}) {
  return request('/web/feedback/getTopQuestions', params);
}
/**
 * 客服页的热门问题详情
 * id
 */
export function ApiFeedGetQuestionsInfo(params = {}) {
  return request('/web/feedback/getQuestionsInfo', params);
}
/**
 * 客服页的热门问题 有用/没有用提交
 */
export function ApiFeedQuestionsFeedback(params = {}) {
  return request('/web/feedback/questionsFeedback', params);
}

/**
 * 客服页的问题类型
 */
export function ApiFeedGetQuestionType(params = {}) {
  return request('/web/feedback/getQuestionType', params);
}

/**
 * 客服页的问题详情里的列表数据
 */
export function ApiFeedGetQuestionList(params = {}) {
  return request('/web/feedback/getQuestionList', params);
}
