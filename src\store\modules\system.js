import { ApiIndexExtra } from '@/api/views/system.js';
import {
  ApiCommentCommentReadStatus,
  ApiCommentGetUnreadCount,
} from '@/api/views/comment.js';
import appIcon from '@/assets/images/app-icon2.png';
import { getDayZeroTimestamp } from '@/utils/function.js';
import { isAndroidBox } from '@/utils/userAgent';
export default {
  state: {
    initData: {},
    // 正在请求的接口，解决重复请求同一接口的问题
    loadingUrl: '',
    // 小红点信息 true:有小红点，false:没有小红点
    dotInfo: {
      feedback_read: false,
      reply_read: false,
      inform_read: false,
    },
    // 未读消息数量
    unreadCount: {
      fk: 0, // 反馈
      hf: 0, // 回复
      tz: 0, // 通知
      sum: 0, // 总数
    },
    homeNav: [],
    clickedTabBarAngle: [],
  },
  getters: {
    initData(state) {
      return state.initData;
    },
    configs(state) {
      return state.initData?.configs;
    },
    // 是否up版
    isUpApp(state) {
      return state.initData?.configs?.flavor == 3;
    },
    appName(state) {
      return state.initData?.app_name ?? '3733游戏盒';
    },
    defaultAvatar(state) {
      return state.initData?.default_avatar ?? appIcon;
    },
    // 渠道下载配置 0=正常显示，1=隐藏，2=敬请期待(暂不处理)
    dlConfig(state) {
      return state.initData?.configs?.dl_config ?? 1;
    },
    // 福利中心显示隐藏 0=正常显示，1=隐藏
    hideJfq(state) {
      return state.initData?.configs?.hide_jfq ?? 0;
    },
    // 显示tabbar“我的游戏” 1是展示 0是隐藏
    showMyGame(state) {
      return state.initData?.configs?.show_my_game ?? 1;
    },
    // 某些渠道需要隐藏引导弹窗和右下角icon
    showSuspension(state) {
      return state.initData?.configs?.show_suspension ?? 1;
    },
    // 客服qq号
    kefuQQNumber(state) {
      return state.initData?.configs?.kefu?.qq || '';
    },
    // 客服qq跳转链接
    kefuQQLink(state) {
      return state.initData?.configs?.kefu?.qq_url || '';
    },
    // 网易客服
    kefuWyLink(state) {
      return (
        state.initData?.configs?.wy_kf_url ||
        'https://qiyukf.com/script/6374f68354737e31410ef7a1b0e1828d.js'
      );
    },
    // 是否接trackingIo统计
    accessTrackingIO(state) {
      return state.initData?.access_tracking_io ?? false;
    },
    loadingUrl(state) {
      return state.loadingUrl;
    },
    dotInfo(state) {
      return state.dotInfo;
    },
    // 未读消息数量
    unreadCount(state) {
      return state.unreadCount;
    },
    homeNav(state) {
      return state.homeNav;
    },
    tabBarAngle(state) {
      return state.initData?.nav_angle || [];
    },
    clickedTabBarAngle(state) {
      return state.clickedTabBarAngle;
    },
    jumpGame(state) {
      return state.initData?.jump_game || {};
    },
    isH5First(state) {
      return state.initData?.is_h5_first || 0;
    },
    // 是否显示交易模块
    showTransaction(state) {
      return state.initData?.configs?.show_transaction || true;
    },
  },
  mutations: {
    setInitData(state, payload) {
      if (payload) {
        state.initData = Object.assign({}, payload);
      } else {
        state.initData = {
          feedback_read: false,
          reply_read: false,
          inform_read: false,
        };
      }
    },
    setLoadingUrl(state, payload) {
      state.loadingUrl = payload || '';
    },
    setDotInfo(state, payload) {
      state.dotInfo = Object.assign({}, payload);
    },
    setUnreadCount(state, payload) {
      state.unreadCount = Object.assign({}, payload);
    },
    setHomeNav(state, payload) {
      if (payload) {
        state.homeNav = payload;
      } else {
        state.homeNav = [];
      }
    },
    setClickedTabBarAngle(state, payload) {
      if (payload) {
        let findResult = state.clickedTabBarAngle.findIndex((item, index) => {
          return item.navigation == payload;
        });
        if (findResult != -1) {
          state.clickedTabBarAngle.splice(findResult, 1);
        }
        state.clickedTabBarAngle.push({
          navigation: payload,
          dateTimeStamp: getDayZeroTimestamp(new Date().getTime() / 1000),
        });
      } else {
        state.clickedTabBarAngle = [];
      }
    },
  },
  actions: {
    async SET_INIT_DATA({ commit }) {
      const res = await ApiIndexExtra();
      commit('setInitData', res.data);
    },
    async SET_DOT_INFO({ commit }) {
      const res = await ApiCommentCommentReadStatus();
      commit('setDotInfo', res.data);
    },
    async SET_UNREAD_COUNT({ commit }) {
      const res = await ApiCommentGetUnreadCount();
      commit('setUnreadCount', res.data);
    },
    async SET_CLICKED_TAB_BAR_ANGLE({ commit, rootState }, index) {
      commit('setClickedTabBarAngle', index);
      localStorage.setItem('STORE', JSON.stringify(rootState));
    },
  },
};
