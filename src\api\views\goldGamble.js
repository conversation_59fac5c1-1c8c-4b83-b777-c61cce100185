import { request } from '../index';

// 金币夺宝列表
/**
 * @param type 类型 1代金券 2金币
 * */
export function ApiGoldDuobaoInfoList(params = {}) {
  return request('/api/gold_duobao/infoList', params);
}

// 本期参与用户
/**
 * @param id 夺宝列表中id
 * */
export function ApiGoldDuobaoPlayUsers(params = {}) {
  return request('/api/gold_duobao/playUsers', params);
}

// 金币夺宝详情
/**
 * @param id 夺宝列表中id
 * @param over 0 非必要参数 1=夺宝结果后商品详情
 * */
export function ApiGoldDuobaoRead(params = {}) {
  return request('/api/gold_duobao/read', params);
}

// 我的夺宝码
/**
 * @param id 夺宝列表中id
 * */
export function ApiGoldDuobaoUserCode(params = {}) {
  return request('/api/gold_duobao/userCode', params);
}

// 购买
/**
 * @param num 2
 * @param id 夺宝列表中id
 * */
export function ApiGoldDuobaoCreateOrder(params = {}) {
  return request('/api/gold_duobao/createOrder', params);
}

// 往期揭秘
/**
 * @param id 夺宝列表中id
 * */
export function ApiGoldDuobaoPastPeriod(params = {}) {
  return request('/api/gold_duobao/pastPeriod', params);
}

// 我的夺宝记录
/**
 * @param status 0全部 1进行中 2已中奖 3未中奖
 * */
export function ApiGoldDuobaoUserRecord(params = {}) {
  return request('/api/gold_duobao/userRecord', params);
}
