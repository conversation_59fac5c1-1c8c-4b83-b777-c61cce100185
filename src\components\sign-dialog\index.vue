<template>
  <div class="sign-dialog">
    <van-dialog
      v-model="installSignDialog"
      :lock-scroll="false"
      :show-confirm-button="false"
    >
      <div class="container">
        <div class="close btn" @click="installSignDialog = false"></div>
        <div class="title">{{ $t('安装个人签版本') }}</div>
        <div class="content">
          {{
            $t(
              '个人签版本游戏为svip用户专享版本，对于掉签问题相对会稳定一些。若下载“个人签版本游戏需先成为svip会员。',
            )
          }}
        </div>
        <div class="download-btn btn">{{ $t('下载个人签版本') }}</div>
        <div class="tips">{{ $t('如何安装个人签版游戏') }}</div>
      </div>
    </van-dialog>
    <van-dialog
      v-model="svipDialog"
      :lock-scroll="false"
      :show-confirm-button="true"
      :showCancelButton="true"
      :confirmButtonText="$t('立即开通')"
      confirmButtonColor="#47A83A"
      :cancelButtonText="$t('再考虑一下')"
      cancelButtonColor="#666666"
      @confirm="toPage('Svip')"
    >
      <div class="container">
        <div class="title">{{ $t('是否开通svip会员') }}</div>
        <div class="content">
          {{
            $t(
              '成为svip用户后可绑定当前设备，绑定完成后,所下载安装的游戏均为个人签版游戏。',
            )
          }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  name: 'SignDialog',
  props: {
    installSignDialogShow: {
      type: Boolean,
      default: false,
    },
    svipDialogShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      installSignDialog: this.installSignDialogShow,
      svipDialog: this.svipDialogShow,
    };
  },
  watch: {
    installSignDialogShow(val) {
      this.installSignDialog = val;
    },
    svipDialogShow(val) {
      this.svipDialog = val;
    },
  },
};
</script>

<style lang="less" scoped>
.sign-dialog {
  /deep/ .van-dialog {
    width: 290 * @rem;
  }
  .container {
    position: relative;
    padding: 25 * @rem 17 * @rem 15 * @rem;
    .close {
      position: absolute;
      right: 0;
      top: 0;
      width: 16 * @rem;
      height: 16 * @rem;
      padding: 13 * @rem;
      background: url(~@/assets/images/close-dialog.png) center center no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    .title {
      font-size: 17 * @rem;
      color: #000000;
      font-weight: bold;
      text-align: center;
    }
    .content {
      font-size: 14 * @rem;
      color: #333333;
      line-height: 20 * @rem;
      text-align: justify;
      margin-top: 15 * @rem;
    }
    .download-btn {
      width: 250 * @rem;
      height: 40 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 20 * @rem auto 0;
      font-size: 15 * @rem;
      color: #ffffff;
      background: @themeBg;
      border-radius: 5 * @rem;
    }
    .tips {
      font-size: 14 * @rem;
      color: #47a83a;
      text-align: center;
      margin-top: 12 * @rem;
    }
  }
}
</style>
