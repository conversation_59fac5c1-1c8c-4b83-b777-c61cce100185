import { getQueryVariable } from '@/utils/function.js';

let uuid = localStorage.getItem('uuid')
  ? localStorage.getItem('uuid')
  : createUuid();

// 加上公共参数
let BASEPARAMS = {
  uuid: uuid,
  versionCode: parseInt(process.env.VUE_APP_versionCode), // 版本号
  build: 100, // 只有ios生效，大于27
};

const cps = getQueryVariable('c');
// url中的渠道号如果是纯数字就拼接上cps，如果含有字母则直接使用 2022年9月1日13:48:15
if (cps) BASEPARAMS.channel = isNaN(cps) ? cps : `cps${cps}`;

export default BASEPARAMS;

// 创建uuid
function createUuid() {
  let d = new Date().getTime();
  if (window.performance && typeof window.performance.now === 'function') {
    d += performance.now(); //use high-precision timer if available
  }
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    function (c) {
      let r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
    },
  );
  localStorage.setItem('uuid', uuid);
  return uuid;
}
