<template>
  <div class="loading-container">
    <!-- <div class="loading-spinner"></div>
    <div class="loading-text">{{ $t('加载中...') }}</div> -->
    <van-loading vertical>加载中...</van-loading>
  </div>
</template>

<script>
export default {
  name: 'LoadingIndicator',
};
</script>

<style lang="less" scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200 * @rem;
  width: 100%;

  .loading-spinner {
    width: 36 * @rem;
    height: 36 * @rem;
    border: 3 * @rem solid rgba(28, 206, 148, 0.2);
    border-top: 3 * @rem solid @themeColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 12 * @rem;
    font-size: 14 * @rem;
    color: #93999f;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 