<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    :overlay-style="{ 'z-index': '2999' }"
    class="first-coupon-popup"
  >
    <div class="content">
      <img class="bg" src="@/assets/images/home/<USER>" />
      <div class="coupon-content">
        <div class="coupon-title">{{ $t('领取首充福利，助你畅玩游戏') }}</div>
        <div class="coupon-list">
          <div class="coupon-item" v-for="item in couponList" :key="item.id">
            <div class="left">
              <img
                src="@/assets/images/home/<USER>"
                alt=""
              />
            </div>
            <div class="center">
              <div class="coupon-name">{{ item.tip }}</div>
              <div class="money">
                <span class="get-money">{{ item.money }}{{ $t('元') }}</span>
                <span class="reach-money"
                  >{{ $t('满') }}{{ item.reach_money }}{{ $t('元可用') }}</span
                >
              </div>
            </div>
            <div class="right">
              {{ $t('有效期') }}{{ item.period }}{{ $t('天') }}
            </div>
          </div>
        </div>
      </div>

      <div class="close" @click="popup = false"></div>
      <div class="first-coupon-get-btn btn" @click="getCoupon">
        {{ $t('一键领取') }}
      </div>
    </div>
  </van-dialog>
</template>

<script>
import {
  ApiCouponGetFirstCouponAjax,
  ApiTakeByFirstCoupon,
} from '@/api/views/coupon.js';
import { mapActions, mapGetters, mapMutations } from 'vuex';
export default {
  data() {
    return {
      couponList: [], // 首充代金券列表
    };
  },
  computed: {
    popup: {
      get() {
        return this.showFCPopup;
      },
      set(value) {
        this.setShowFCPopup(value);
      },
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
      showFCPopup: 'user/showFCPopup',
    }),
  },
  async created() {
    await this.getCouponList();
  },
  methods: {
    ...mapActions({
      SET_FIRST_COUPON_ICON: 'user/SET_FIRST_COUPON_ICON',
    }),
    ...mapMutations({
      setShowFCPopup: 'user/setShowFCPopup',
    }),
    // 获取首充代金券
    async getCouponList() {
      const res = await ApiCouponGetFirstCouponAjax();
      this.couponList = res.data.coupon;
    },
    // 一键领取首充代金券
    async getCoupon() {
      this.popup = false;
      if (!this.userInfo.token) {
        this.$nextTick(() => {
          this.toPage('PhoneLogin');
        });
        return false;
      }
      const res = await ApiTakeByFirstCoupon();
      await this.SET_FIRST_COUPON_ICON();
      this.$nextTick(() => {
        this.$toast(res.msg);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.first-coupon-popup {
  background-color: rgba(0, 0, 0, 0);
  width: 360 * @rem;
  z-index: 2999 !important;
  .content {
    position: relative;
  }
  .bg {
    display: block;
    width: 301 * @rem;
    height: 428 * @rem;
    margin: 55 * @rem auto 0;
  }
  .close {
    position: absolute;
    right: 20 * @rem;
    top: 55 * @rem;
    width: 26 * @rem;
    height: 26 * @rem;
    background-image: url(~@/assets/images/close.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .coupon-content {
    width: 100%;
    position: absolute;
    top: 187 * @rem;
    left: 0;
    .coupon-title {
      text-align: center;
      font-size: 14 * @rem;
      color: #ffffff;
    }
    .coupon-list {
      width: 235 * @rem;
      margin: 10 * @rem auto 0;
      .coupon-item {
        width: 235 * @rem;
        height: 60 * @rem;
        margin-top: 8 * @rem;
        background: url(~@/assets/images/home/<USER>
          no-repeat;
        background-size: 235 * @rem 60 * @rem;
        display: flex;
        align-items: center;
        &:nth-of-type(1) {
          margin-top: 0;
        }
        .left {
          width: 44 * @rem;
          height: 44 * @rem;
          margin-left: 8 * @rem;
        }
        .center {
          flex: 1;
          min-width: 0;
          margin-left: 6 * @rem;
          .coupon-name {
            font-size: 14 * @rem;
            color: #000000;
          }
          .money {
            display: flex;
            align-items: flex-end;
            margin-top: 5 * @rem;
            .get-money {
              font-size: 13 * @rem;
              color: #ff2b2b;
            }
            .reach-money {
              font-size: 9 * @rem;
              color: #a6a6a6;
              margin-left: 6 * @rem;
            }
          }
        }
        .right {
          width: 64 * @rem;
          font-size: 9 * @rem;
          color: #a6a6a6;
          height: 36 * @rem;
          background: url(~@/assets/images/home/<USER>
            center no-repeat;
          background-size: 1 * @rem auto;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  .first-coupon-get-btn {
    width: 208 * @rem;
    height: 48 * @rem;
    background: url(~@/assets/images/home/<USER>
      center no-repeat;
    background-size: 208 * @rem 48 * @rem;
    margin: 16 * @rem auto 0;
    font-size: 20 * @rem;
    font-weight: bold;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
